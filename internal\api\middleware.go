package api

import (
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"golang.org/x/time/rate"

	"aistudio-proxy/internal/config"
)

// RequestIDMiddleware adds a unique request ID to each request
func RequestIDMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		requestID := c.GetHeader("X-Request-ID")
		if requestID == "" {
			requestID = uuid.New().String()
		}
		c.Set("request_id", requestID)
		c.Header("X-Request-ID", requestID)
		c.Next()
	}
}

// GetRequestID retrieves the request ID from the context
func GetRequestID(c *gin.Context) string {
	if requestID, exists := c.Get("request_id"); exists {
		return requestID.(string)
	}
	return uuid.New().String()
}

// LoggingMiddleware logs HTTP requests
func LoggingMiddleware(logger *logrus.Logger) gin.HandlerFunc {
	return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		logger.WithFields(logrus.Fields{
			"status_code":   param.StatusCode,
			"latency":       param.Latency,
			"client_ip":     param.ClientIP,
			"method":        param.Method,
			"path":          param.Path,
			"user_agent":    param.Request.UserAgent(),
			"response_size": param.BodySize,
		}).Info("HTTP request")
		return ""
	})
}

// AuthMiddleware validates API key authentication
func AuthMiddleware(cfg *config.Config) gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": map[string]interface{}{
					"code":    "MISSING_AUTHORIZATION",
					"message": "Authorization header is required",
				},
			})
			c.Abort()
			return
		}

		// Check for Bearer token format
		parts := strings.SplitN(authHeader, " ", 2)
		if len(parts) != 2 || parts[0] != "Bearer" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": map[string]interface{}{
					"code":    "INVALID_AUTHORIZATION_FORMAT",
					"message": "Authorization header must be in format 'Bearer <token>'",
				},
			})
			c.Abort()
			return
		}

		token := parts[1]
		
		// Validate token against configured API keys
		validToken := false
		for _, apiKey := range cfg.Auth.APIKeys {
			if token == apiKey {
				validToken = true
				break
			}
		}

		if !validToken {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": map[string]interface{}{
					"code":    "INVALID_API_KEY",
					"message": "Invalid API key",
				},
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// CORSMiddleware handles Cross-Origin Resource Sharing
func CORSMiddleware(cfg *config.Config) gin.HandlerFunc {
	return func(c *gin.Context) {
		if !cfg.CORS.Enabled {
			c.Next()
			return
		}

		origin := c.Request.Header.Get("Origin")
		
		// Check if origin is allowed
		allowedOrigin := ""
		for _, allowedOrig := range cfg.CORS.AllowedOrigins {
			if allowedOrig == "*" || allowedOrig == origin {
				allowedOrigin = allowedOrig
				break
			}
		}

		if allowedOrigin != "" {
			if allowedOrigin == "*" {
				c.Header("Access-Control-Allow-Origin", "*")
			} else {
				c.Header("Access-Control-Allow-Origin", origin)
			}
		}

		// Set other CORS headers
		c.Header("Access-Control-Allow-Methods", strings.Join(cfg.CORS.AllowedMethods, ", "))
		c.Header("Access-Control-Allow-Headers", strings.Join(cfg.CORS.AllowedHeaders, ", "))
		c.Header("Access-Control-Expose-Headers", strings.Join(cfg.CORS.ExposedHeaders, ", "))
		
		if cfg.CORS.AllowCredentials {
			c.Header("Access-Control-Allow-Credentials", "true")
		}
		
		if cfg.CORS.MaxAge > 0 {
			c.Header("Access-Control-Max-Age", string(rune(cfg.CORS.MaxAge)))
		}

		// Handle preflight requests
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}

// RateLimitMiddleware implements rate limiting per session/IP
func RateLimitMiddleware(cfg *config.Config) gin.HandlerFunc {
	// Simple in-memory rate limiter
	limiters := make(map[string]*rate.Limiter)
	
	return func(c *gin.Context) {
		if !cfg.RateLimit.Enabled {
			c.Next()
			return
		}

		// Use session ID if available, otherwise use client IP
		key := c.GetHeader("X-Session-ID")
		if key == "" {
			key = c.ClientIP()
		}

		// Get or create limiter for this key
		limiter, exists := limiters[key]
		if !exists {
			limiter = rate.NewLimiter(
				rate.Every(time.Minute/time.Duration(cfg.RateLimit.RequestsPerMinute)),
				cfg.RateLimit.BurstSize,
			)
			limiters[key] = limiter
		}

		// Check rate limit
		if !limiter.Allow() {
			c.JSON(http.StatusTooManyRequests, gin.H{
				"error": map[string]interface{}{
					"code":    "RATE_LIMIT_EXCEEDED",
					"message": "Rate limit exceeded. Please try again later.",
					"details": map[string]interface{}{
						"limit":      cfg.RateLimit.RequestsPerMinute,
						"window":     "1 minute",
						"retry_after": 60,
					},
				},
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequestSizeLimitMiddleware limits request body size
func RequestSizeLimitMiddleware(cfg *config.Config) gin.HandlerFunc {
	return func(c *gin.Context) {
		if c.Request.ContentLength > cfg.Limits.MaxRequestSize {
			c.JSON(http.StatusRequestEntityTooLarge, gin.H{
				"error": map[string]interface{}{
					"code":    "REQUEST_TOO_LARGE",
					"message": "Request body too large",
					"details": map[string]interface{}{
						"max_size": cfg.Limits.MaxRequestSize,
						"received": c.Request.ContentLength,
					},
				},
			})
			c.Abort()
			return
		}
		c.Next()
	}
}

// SecurityHeadersMiddleware adds security headers
func SecurityHeadersMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("X-Content-Type-Options", "nosniff")
		c.Header("X-Frame-Options", "DENY")
		c.Header("X-XSS-Protection", "1; mode=block")
		c.Header("Referrer-Policy", "strict-origin-when-cross-origin")
		c.Header("Content-Security-Policy", "default-src 'self'")
		c.Next()
	}
}
