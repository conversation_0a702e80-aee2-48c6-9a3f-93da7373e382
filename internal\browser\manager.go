package browser

import (
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"time"

	"github.com/playwright-community/playwright-go"
	"github.com/sirupsen/logrus"

	"aistudio-proxy/internal/config"
)

// Manager manages browser instances and contexts
type Manager struct {
	config     *config.Config
	logger     *logrus.Logger
	playwright *playwright.Playwright
	browser    playwright.Browser
	contexts   map[string]*BrowserContext
	contextsMu sync.RWMutex
	closed     bool
	closeMu    sync.RWMutex
}

// BrowserContext represents a browser context with metadata
type BrowserContext struct {
	Context   playwright.BrowserContext
	SessionID string
	CreatedAt time.Time
	LastUsed  time.Time
	mu        sync.RWMutex
}

// NewManager creates a new browser manager
func NewManager(cfg *config.Config, logger *logrus.Logger) (*Manager, error) {
	// Install Playwright if needed
	err := playwright.Install()
	if err != nil {
		return nil, fmt.Errorf("failed to install playwright: %w", err)
	}

	// Start Playwright
	pw, err := playwright.Run()
	if err != nil {
		return nil, fmt.E<PERSON><PERSON>("failed to start playwright: %w", err)
	}

	manager := &Manager{
		config:     cfg,
		logger:     logger,
		playwright: pw,
		contexts:   make(map[string]*BrowserContext),
	}

	// Launch browser
	if err := manager.launchBrowser(); err != nil {
		pw.Stop()
		return nil, fmt.Errorf("failed to launch browser: %w", err)
	}

	// Start cleanup routine
	go manager.cleanupRoutine()

	logger.WithFields(logrus.Fields{
		"browser_type": cfg.Browser.Type,
		"headless":     cfg.Browser.Headless,
		"max_contexts": cfg.Browser.MaxContexts,
	}).Info("Browser manager initialized")

	return manager, nil
}

// launchBrowser launches the browser instance
func (m *Manager) launchBrowser() error {
	var browserType playwright.BrowserType

	switch m.config.Browser.Type {
	case "chrome", "chromium":
		browserType = m.playwright.Chromium
	case "firefox":
		browserType = m.playwright.Firefox
	case "webkit", "safari":
		browserType = m.playwright.WebKit
	default:
		return fmt.Errorf("unsupported browser type: %s", m.config.Browser.Type)
	}

	// Prepare launch options
	options := playwright.BrowserTypeLaunchOptions{
		Headless: &m.config.Browser.Headless,
		Args:     m.config.Browser.Args,
	}

	// Set user data directory if profile path is specified
	if m.config.Browser.ProfilePath != "" {
		// Ensure profile directory exists
		if err := os.MkdirAll(m.config.Browser.ProfilePath, 0755); err != nil {
			return fmt.Errorf("failed to create profile directory: %w", err)
		}

		userDataDir := filepath.Join(m.config.Browser.ProfilePath, "user-data")
		options.Args = append(options.Args, fmt.Sprintf("--user-data-dir=%s", userDataDir))
	}

	// Launch browser with timeout
	// Note: Playwright Go doesn't have LaunchWithContext, we use Launch with timeout handling
	browser, err := browserType.Launch(options)
	if err != nil {
		return fmt.Errorf("failed to launch browser: %w", err)
	}

	m.browser = browser
	return nil
}

// GetContext gets or creates a browser context for a session
func (m *Manager) GetContext(sessionID string) (*BrowserContext, error) {
	m.closeMu.RLock()
	if m.closed {
		m.closeMu.RUnlock()
		return nil, fmt.Errorf("browser manager is closed")
	}
	m.closeMu.RUnlock()

	m.contextsMu.Lock()
	defer m.contextsMu.Unlock()

	// Check if context already exists
	if ctx, exists := m.contexts[sessionID]; exists {
		ctx.mu.Lock()
		ctx.LastUsed = time.Now()
		ctx.mu.Unlock()
		return ctx, nil
	}

	// Check if we've reached the maximum number of contexts
	if len(m.contexts) >= m.config.Browser.MaxContexts {
		return nil, fmt.Errorf("maximum number of browser contexts reached (%d)", m.config.Browser.MaxContexts)
	}

	// Create new context
	contextOptions := playwright.BrowserNewContextOptions{
		UserAgent: playwright.String("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"),
		Viewport: &playwright.Size{
			Width:  1920,
			Height: 1080,
		},
		AcceptDownloads:   playwright.Bool(true),
		JavaScriptEnabled: playwright.Bool(true),
	}

	// Set locale and timezone
	contextOptions.Locale = playwright.String("en-US")
	contextOptions.TimezoneId = playwright.String("America/New_York")

	// Create context with timeout
	// Note: Playwright Go doesn't have NewContextWithContext, we use NewContext
	browserContext, err := m.browser.NewContext(contextOptions)
	if err != nil {
		return nil, fmt.Errorf("failed to create browser context: %w", err)
	}

	// Create wrapper
	contextWrapper := &BrowserContext{
		Context:   browserContext,
		SessionID: sessionID,
		CreatedAt: time.Now(),
		LastUsed:  time.Now(),
	}

	m.contexts[sessionID] = contextWrapper

	m.logger.WithFields(logrus.Fields{
		"session_id":     sessionID,
		"total_contexts": len(m.contexts),
	}).Debug("Created new browser context")

	return contextWrapper, nil
}

// RemoveContext removes a browser context
func (m *Manager) RemoveContext(sessionID string) error {
	m.contextsMu.Lock()
	defer m.contextsMu.Unlock()

	ctx, exists := m.contexts[sessionID]
	if !exists {
		return fmt.Errorf("context not found for session: %s", sessionID)
	}

	// Close the context
	if err := ctx.Context.Close(); err != nil {
		m.logger.WithError(err).WithField("session_id", sessionID).Warn("Failed to close browser context")
	}

	delete(m.contexts, sessionID)

	m.logger.WithFields(logrus.Fields{
		"session_id":     sessionID,
		"total_contexts": len(m.contexts),
	}).Debug("Removed browser context")

	return nil
}

// GetContextCount returns the current number of active contexts
func (m *Manager) GetContextCount() int {
	m.contextsMu.RLock()
	defer m.contextsMu.RUnlock()
	return len(m.contexts)
}

// cleanupRoutine periodically cleans up expired contexts
func (m *Manager) cleanupRoutine() {
	ticker := time.NewTicker(m.config.Session.CleanupInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			m.cleanupExpiredContexts()
		}
	}
}

// cleanupExpiredContexts removes expired browser contexts
func (m *Manager) cleanupExpiredContexts() {
	m.contextsMu.Lock()
	defer m.contextsMu.Unlock()

	now := time.Now()
	expiredSessions := make([]string, 0)

	for sessionID, ctx := range m.contexts {
		ctx.mu.RLock()
		if now.Sub(ctx.LastUsed) > m.config.Browser.ContextTimeout {
			expiredSessions = append(expiredSessions, sessionID)
		}
		ctx.mu.RUnlock()
	}

	for _, sessionID := range expiredSessions {
		ctx := m.contexts[sessionID]
		if err := ctx.Context.Close(); err != nil {
			m.logger.WithError(err).WithField("session_id", sessionID).Warn("Failed to close expired context")
		}
		delete(m.contexts, sessionID)

		m.logger.WithField("session_id", sessionID).Debug("Cleaned up expired browser context")
	}

	if len(expiredSessions) > 0 {
		m.logger.WithFields(logrus.Fields{
			"cleaned_contexts": len(expiredSessions),
			"total_contexts":   len(m.contexts),
		}).Info("Cleaned up expired browser contexts")
	}
}

// Close closes the browser manager and all contexts
func (m *Manager) Close() error {
	m.closeMu.Lock()
	defer m.closeMu.Unlock()

	if m.closed {
		return nil
	}

	m.closed = true

	// Close all contexts
	m.contextsMu.Lock()
	for sessionID, ctx := range m.contexts {
		if err := ctx.Context.Close(); err != nil {
			m.logger.WithError(err).WithField("session_id", sessionID).Warn("Failed to close context during shutdown")
		}
	}
	m.contexts = make(map[string]*BrowserContext)
	m.contextsMu.Unlock()

	// Close browser
	if m.browser != nil {
		if err := m.browser.Close(); err != nil {
			m.logger.WithError(err).Warn("Failed to close browser")
		}
	}

	// Stop Playwright
	if m.playwright != nil {
		if err := m.playwright.Stop(); err != nil {
			m.logger.WithError(err).Warn("Failed to stop Playwright")
		}
	}

	m.logger.Info("Browser manager closed")
	return nil
}
