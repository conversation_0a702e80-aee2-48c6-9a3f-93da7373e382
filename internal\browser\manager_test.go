package browser

import (
	"testing"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"

	"aistudio-proxy/internal/config"
)

// TestManagerCreation tests that the browser manager can be created with correct configuration
func TestManagerCreation(t *testing.T) {
	cfg := &config.Config{
		Browser: config.BrowserConfig{
			Type:           "chrome",
			Headless:       true,
			LaunchTimeout:  30 * time.Second,
			MaxContexts:    10,
			ContextTimeout: 3600 * time.Second,
			Args:           []string{"--no-sandbox", "--disable-dev-shm-usage"},
		},
		Session: config.SessionConfig{
			CleanupInterval: 5 * time.Minute,
		},
	}

	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel) // Reduce noise in tests

	// Note: We can't actually create a manager in tests without Playwright installed
	// This test just verifies the configuration structure is correct
	assert.NotNil(t, cfg)
	assert.Equal(t, "chrome", cfg.Browser.Type)
	assert.True(t, cfg.Browser.Headless)
	assert.Equal(t, 10, cfg.Browser.MaxContexts)
}

// TestBrowserContextStructure tests the BrowserContext structure
func TestBrowserContextStructure(t *testing.T) {
	ctx := &BrowserContext{
		SessionID: "test-session-123",
		CreatedAt: time.Now(),
		LastUsed:  time.Now(),
	}

	assert.Equal(t, "test-session-123", ctx.SessionID)
	assert.False(t, ctx.CreatedAt.IsZero())
	assert.False(t, ctx.LastUsed.IsZero())
}

// TestManagerConfiguration tests manager configuration validation
func TestManagerConfiguration(t *testing.T) {
	tests := []struct {
		name        string
		browserType string
		expectValid bool
	}{
		{"Chrome", "chrome", true},
		{"Chromium", "chromium", true},
		{"Firefox", "firefox", true},
		{"WebKit", "webkit", true},
		{"Invalid", "invalid-browser", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cfg := &config.Config{
				Browser: config.BrowserConfig{
					Type:           tt.browserType,
					Headless:       true,
					LaunchTimeout:  30 * time.Second,
					MaxContexts:    10,
					ContextTimeout: 3600 * time.Second,
				},
			}

			// This would be used in actual manager creation
			assert.Equal(t, tt.browserType, cfg.Browser.Type)
		})
	}
}
