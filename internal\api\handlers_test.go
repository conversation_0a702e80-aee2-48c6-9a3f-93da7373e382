package api

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"aistudio-proxy/internal/aistudio"
	"aistudio-proxy/internal/config"
	"aistudio-proxy/internal/session"
)

// MockBrowserManager is a mock implementation of browser.Manager
type MockBrowserManager struct {
	mock.Mock
}

func (m *MockBrowserManager) GetContext(sessionID string) (*MockBrowserContext, error) {
	args := m.Called(sessionID)
	return args.Get(0).(*MockBrowserContext), args.Error(1)
}

func (m *MockBrowserManager) RemoveContext(sessionID string) error {
	args := m.Called(sessionID)
	return args.Error(0)
}

func (m *MockBrowserManager) GetContextCount() int {
	args := m.Called()
	return args.Int(0)
}

func (m *MockBrowserManager) Close() error {
	args := m.Called()
	return args.Error(0)
}

// MockBrowserContext is a mock browser context
type MockBrowserContext struct {
	SessionID string
}

// MockSessionManager is a mock implementation of session.Manager
type MockSessionManager struct {
	mock.Mock
}

func (m *MockSessionManager) CreateSession(req *session.CreateSessionRequest) (*session.CreateSessionResponse, error) {
	args := m.Called(req)
	return args.Get(0).(*session.CreateSessionResponse), args.Error(1)
}

func (m *MockSessionManager) GetSession(sessionID string) (*session.Session, error) {
	args := m.Called(sessionID)
	return args.Get(0).(*session.Session), args.Error(1)
}

func (m *MockSessionManager) DeleteSession(sessionID string) error {
	args := m.Called(sessionID)
	return args.Error(0)
}

func (m *MockSessionManager) GetSessionCount() int {
	args := m.Called()
	return args.Int(0)
}

func (m *MockSessionManager) GetOrCreateSession(sessionID string, req *session.CreateSessionRequest) (*session.Session, bool, error) {
	args := m.Called(sessionID, req)
	return args.Get(0).(*session.Session), args.Bool(1), args.Error(2)
}

func (m *MockSessionManager) RenewSession(sessionID string, ttlMinutes int) error {
	args := m.Called(sessionID, ttlMinutes)
	return args.Error(0)
}

func (m *MockSessionManager) ListSessions() ([]session.SessionInfo, error) {
	args := m.Called()
	return args.Get(0).([]session.SessionInfo), args.Error(1)
}

func (m *MockSessionManager) Close() error {
	args := m.Called()
	return args.Error(0)
}

// MockAIStudioClient is a mock implementation of aistudio.Client
type MockAIStudioClient struct {
	mock.Mock
}

func (m *MockAIStudioClient) SendMessage(req *aistudio.ChatRequest) (*aistudio.ChatResponse, error) {
	args := m.Called(req)
	return args.Get(0).(*aistudio.ChatResponse), args.Error(1)
}

func (m *MockAIStudioClient) GetAvailableModels(sessionID string) ([]aistudio.Model, error) {
	args := m.Called(sessionID)
	return args.Get(0).([]aistudio.Model), args.Error(1)
}

// setupTestHandlers creates handlers with mocked dependencies
func setupTestHandlers() (*Handlers, *MockBrowserManager, *MockSessionManager) {
	cfg := &config.Config{
		Auth: config.AuthConfig{
			APIKeys: []string{"test-api-key"},
			Enabled: true,
		},
		Limits: config.LimitsConfig{
			MaxMessageLength: 10000,
			MaxFileSize:      10485760,
			AllowedFileTypes: []string{"jpg", "png", "pdf", "txt"},
		},
	}

	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel) // Reduce noise in tests

	mockBrowserManager := &MockBrowserManager{}
	mockSessionManager := &MockSessionManager{}

	handlers := &Handlers{
		config:         cfg,
		logger:         logger,
		browserManager: mockBrowserManager,
		sessionManager: mockSessionManager,
	}

	return handlers, mockBrowserManager, mockSessionManager
}

// setupTestRouter creates a test router with middleware
func setupTestRouter(handlers *Handlers) *gin.Engine {
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add middleware
	router.Use(RequestIDMiddleware())
	
	// Add auth middleware for API routes
	apiGroup := router.Group("/api/v1")
	apiGroup.Use(AuthMiddleware(handlers.config))

	// Register routes
	handlers.RegisterRoutes(apiGroup)
	router.GET("/health", handlers.HealthCheck)

	return router
}

func TestHealthCheck(t *testing.T) {
	handlers, mockBrowserManager, mockSessionManager := setupTestHandlers()
	router := setupTestRouter(handlers)

	// Setup mocks
	mockBrowserManager.On("GetContextCount").Return(5)
	mockSessionManager.On("GetSessionCount").Return(3)

	// Create request
	req, _ := http.NewRequest("GET", "/health", nil)
	w := httptest.NewRecorder()

	// Execute request
	router.ServeHTTP(w, req)

	// Assert response
	assert.Equal(t, http.StatusOK, w.Code)

	var response HealthResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "healthy", response.Status)
	assert.Equal(t, 3, response.ActiveSessions)

	mockBrowserManager.AssertExpectations(t)
	mockSessionManager.AssertExpectations(t)
}

func TestChatEndpoint_Success(t *testing.T) {
	handlers, mockBrowserManager, mockSessionManager := setupTestHandlers()
	router := setupTestRouter(handlers)

	// Setup mocks
	mockSession := &session.Session{
		ID:    "test-session-id",
		Model: "gemini-pro",
	}
	mockSessionManager.On("GetOrCreateSession", "", mock.AnythingOfType("*session.CreateSessionRequest")).
		Return(mockSession, true, nil)

	// Create request
	chatReq := aistudio.ChatRequest{
		Message: "Hello, world!",
		Model:   "gemini-pro",
	}
	reqBody, _ := json.Marshal(chatReq)

	req, _ := http.NewRequest("POST", "/api/v1/chat", bytes.NewBuffer(reqBody))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer test-api-key")
	w := httptest.NewRecorder()

	// Execute request
	router.ServeHTTP(w, req)

	// Assert response
	assert.Equal(t, http.StatusOK, w.Code)

	mockSessionManager.AssertExpectations(t)
}

func TestChatEndpoint_InvalidAuth(t *testing.T) {
	handlers, _, _ := setupTestHandlers()
	router := setupTestRouter(handlers)

	// Create request without auth
	chatReq := aistudio.ChatRequest{
		Message: "Hello, world!",
	}
	reqBody, _ := json.Marshal(chatReq)

	req, _ := http.NewRequest("POST", "/api/v1/chat", bytes.NewBuffer(reqBody))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()

	// Execute request
	router.ServeHTTP(w, req)

	// Assert response
	assert.Equal(t, http.StatusUnauthorized, w.Code)
}

func TestChatEndpoint_InvalidRequest(t *testing.T) {
	handlers, _, _ := setupTestHandlers()
	router := setupTestRouter(handlers)

	// Create request with empty message
	chatReq := aistudio.ChatRequest{
		Message: "",
	}
	reqBody, _ := json.Marshal(chatReq)

	req, _ := http.NewRequest("POST", "/api/v1/chat", bytes.NewBuffer(reqBody))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer test-api-key")
	w := httptest.NewRecorder()

	// Execute request
	router.ServeHTTP(w, req)

	// Assert response
	assert.Equal(t, http.StatusBadRequest, w.Code)
}

func TestCreateSession(t *testing.T) {
	handlers, _, mockSessionManager := setupTestHandlers()
	router := setupTestRouter(handlers)

	// Setup mocks
	mockResponse := &session.CreateSessionResponse{
		SessionID: "new-session-id",
		Model:     "gemini-pro",
	}
	mockSessionManager.On("CreateSession", mock.AnythingOfType("*session.CreateSessionRequest")).
		Return(mockResponse, nil)

	// Create request
	sessionReq := session.CreateSessionRequest{
		Model:      "gemini-pro",
		TTLMinutes: 60,
	}
	reqBody, _ := json.Marshal(sessionReq)

	req, _ := http.NewRequest("POST", "/api/v1/session", bytes.NewBuffer(reqBody))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer test-api-key")
	w := httptest.NewRecorder()

	// Execute request
	router.ServeHTTP(w, req)

	// Assert response
	assert.Equal(t, http.StatusOK, w.Code)

	var response session.CreateSessionResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "new-session-id", response.SessionID)

	mockSessionManager.AssertExpectations(t)
}

func TestDeleteSession(t *testing.T) {
	handlers, mockBrowserManager, mockSessionManager := setupTestHandlers()
	router := setupTestRouter(handlers)

	// Setup mocks
	mockSessionManager.On("DeleteSession", "test-session-id").Return(nil)
	mockBrowserManager.On("RemoveContext", "test-session-id").Return(nil)

	// Create request
	req, _ := http.NewRequest("DELETE", "/api/v1/session/test-session-id", nil)
	req.Header.Set("Authorization", "Bearer test-api-key")
	w := httptest.NewRecorder()

	// Execute request
	router.ServeHTTP(w, req)

	// Assert response
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]string
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "deleted", response["status"])

	mockSessionManager.AssertExpectations(t)
	mockBrowserManager.AssertExpectations(t)
}
