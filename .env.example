# AI Studio Proxy Environment Configuration
# Copy this file to .env and customize the values

# =============================================================================
# REQUIRED CONFIGURATION
# =============================================================================

# API Keys for authentication (comma-separated)
API_KEYS=your-secret-api-key-1,your-secret-api-key-2

# Google AI Studio credentials
AI_STUDIO_EMAIL=<EMAIL>
AI_STUDIO_PASSWORD=your-password

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================

# Server port and host
PORT=8080
HOST=0.0.0.0

# =============================================================================
# BROWSER CONFIGURATION
# =============================================================================

# Browser type: chrome, chromium, opera, firefox
BROWSER_TYPE=chrome

# Path to browser profile directory (optional)
BROWSER_PROFILE_PATH=./profiles

# Run browser in headless mode (true/false)
HEADLESS=true

# =============================================================================
# AI STUDIO CONFIGURATION
# =============================================================================

# Default AI model to use
AI_STUDIO_DEFAULT_MODEL=gemini-pro

# =============================================================================
# SESSION CONFIGURATION
# =============================================================================

# Default session TTL in minutes
SESSION_TTL_MINUTES=60

# =============================================================================
# AUTHENTICATION CONFIGURATION
# =============================================================================

# Enable API key authentication (true/false)
AUTH_ENABLED=true

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

# Log level: DEBUG, INFO, WARN, ERROR
LOG_LEVEL=INFO

# Log format: json, text
LOG_FORMAT=json

# =============================================================================
# DEVELOPMENT CONFIGURATION
# =============================================================================

# Enable development mode (true/false)
DEVELOPMENT_MODE=false

# Mock AI Studio responses for testing (true/false)
MOCK_RESPONSES=false

# =============================================================================
# DOCKER COMPOSE CONFIGURATION
# =============================================================================

# Version for Docker builds
VERSION=latest

# Build time (automatically set during build)
BUILD_TIME=

# Git commit hash (automatically set during build)
GIT_COMMIT=

# Grafana admin password (for monitoring stack)
GRAFANA_PASSWORD=admin

# =============================================================================
# OPTIONAL ADVANCED CONFIGURATION
# =============================================================================

# Rate limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS_PER_MINUTE=10
RATE_LIMIT_BURST_SIZE=20

# Request limits
MAX_REQUEST_SIZE=1048576
MAX_FILE_SIZE=10485760
MAX_MESSAGE_LENGTH=10000

# CORS configuration
CORS_ENABLED=true
CORS_ALLOWED_ORIGINS=*

# Session management
MAX_SESSIONS=100
SESSION_CLEANUP_INTERVAL=5m

# Browser management
MAX_BROWSER_CONTEXTS=10
BROWSER_CONTEXT_TIMEOUT=3600s

# Retry configuration
RETRY_ENABLED=true
RETRY_MAX_RETRIES=3
RETRY_INITIAL_DELAY=1s
RETRY_MAX_DELAY=30s

# Health check configuration
HEALTH_DETAILED=true
HEALTH_TIMEOUT=5s

# Metrics configuration
METRICS_ENABLED=true
METRICS_DETAILED=false
