package main

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"

	"aistudio-proxy/internal/api"
	"aistudio-proxy/internal/browser"
	"aistudio-proxy/internal/config"
	"aistudio-proxy/internal/session"
)

// Build information set by ldflags
var (
	Version   = "dev"
	BuildTime = "unknown"
	GitCommit = "unknown"
)

// @title AI Studio Proxy API
// @version 1.0
// @description Production-ready proxy server for Google AI Studio
// @termsOfService http://swagger.io/terms/

// @contact.name API Support
// @contact.url http://www.example.com/support
// @contact.email <EMAIL>

// @license.name MIT
// @license.url https://opensource.org/licenses/MIT

// @host localhost:8080
// @BasePath /api/v1

// @securityDefinitions.apikey BearerAuth
// @in header
// @name Authorization
// @description Type "Bearer" followed by a space and JWT token.

func main() {
	// Initialize logger
	logger := logrus.New()
	logger.SetFormatter(&logrus.JSONFormatter{})

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		logger.WithError(err).Fatal("Failed to load configuration")
	}

	// Set log level
	level, err := logrus.ParseLevel(cfg.Logging.Level)
	if err != nil {
		logger.WithError(err).Warn("Invalid log level, using INFO")
		level = logrus.InfoLevel
	}
	logger.SetLevel(level)

	// Log startup information
	logger.WithFields(logrus.Fields{
		"version":    Version,
		"build_time": BuildTime,
		"git_commit": GitCommit,
		"port":       cfg.Server.Port,
	}).Info("Starting AI Studio Proxy Server")

	// Initialize browser manager
	browserManager, err := browser.NewManager(cfg, logger)
	if err != nil {
		logger.WithError(err).Fatal("Failed to initialize browser manager")
	}
	defer browserManager.Close()

	// Initialize session manager
	sessionManager := session.NewManager(cfg, logger)
	defer sessionManager.Close()

	// Set Gin mode based on log level
	if level <= logrus.DebugLevel {
		gin.SetMode(gin.DebugMode)
	} else {
		gin.SetMode(gin.ReleaseMode)
	}

	// Initialize router
	router := gin.New()

	// Add middleware
	router.Use(gin.Recovery())
	router.Use(api.RequestIDMiddleware())
	router.Use(api.LoggingMiddleware(logger))
	router.Use(api.CORSMiddleware(cfg))

	// Add authentication middleware for API routes
	apiGroup := router.Group("/api/v1")
	if cfg.Auth.Enabled {
		apiGroup.Use(api.AuthMiddleware(cfg))
	}

	// Add rate limiting middleware
	if cfg.RateLimit.Enabled {
		apiGroup.Use(api.RateLimitMiddleware(cfg))
	}

	// Initialize API handlers
	apiHandlers := api.NewHandlers(cfg, logger, browserManager, sessionManager)

	// Register API routes
	apiHandlers.RegisterRoutes(apiGroup)

	// Health check endpoint (no auth required)
	router.GET(cfg.Health.Path, apiHandlers.HealthCheck)

	// Metrics endpoint (no auth required)
	if cfg.Metrics.Enabled {
		router.GET(cfg.Metrics.Path, apiHandlers.Metrics)
	}

	// Swagger documentation
	if cfg.Development.Enabled {
		router.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))
	}

	// Create HTTP server
	server := &http.Server{
		Addr:         fmt.Sprintf("%s:%d", cfg.Server.Host, cfg.Server.Port),
		Handler:      router,
		ReadTimeout:  cfg.Server.ReadTimeout,
		WriteTimeout: cfg.Server.WriteTimeout,
		IdleTimeout:  cfg.Server.IdleTimeout,
	}

	// Start server in a goroutine
	go func() {
		logger.WithField("addr", server.Addr).Info("Server starting")
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.WithError(err).Fatal("Failed to start server")
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.Info("Server shutting down...")

	// Create shutdown context with timeout
	ctx, cancel := context.WithTimeout(context.Background(), cfg.Server.ShutdownTimeout)
	defer cancel()

	// Shutdown server
	if err := server.Shutdown(ctx); err != nil {
		logger.WithError(err).Error("Server forced to shutdown")
	} else {
		logger.Info("Server shutdown complete")
	}
}
