package api

import (
	"context"
	"fmt"
	"math"
	"math/rand"
	"time"

	"github.com/sirupsen/logrus"

	"aistudio-proxy/internal/config"
)

// RetryConfig holds retry configuration
type RetryConfig struct {
	MaxRetries        int
	InitialDelay      time.Duration
	MaxDelay          time.Duration
	BackoffMultiplier float64
	Jitter            float64
}

// RetryableFunc represents a function that can be retried
type RetryableFunc func(ctx context.Context) error

// RetryableError represents an error that can be retried
type RetryableError struct {
	Err       error
	Retryable bool
}

func (e *RetryableError) Error() string {
	return e.Err.Error()
}

func (e *RetryableError) Unwrap() error {
	return e.Err
}

// NewRetryableError creates a new retryable error
func NewRetryableError(err error, retryable bool) *RetryableError {
	return &RetryableError{
		Err:       err,
		Retryable: retryable,
	}
}

// RetryWithBackoff executes a function with exponential backoff retry logic
func RetryWithBackoff(ctx context.Context, cfg *config.Config, logger *logrus.Logger, fn RetryableFunc) error {
	if !cfg.Retry.Enabled {
		return fn(ctx)
	}

	var lastErr error
	delay := cfg.Retry.InitialDelay

	for attempt := 0; attempt <= cfg.Retry.MaxRetries; attempt++ {
		// Execute the function
		err := fn(ctx)
		if err == nil {
			if attempt > 0 {
				logger.WithFields(logrus.Fields{
					"attempt":       attempt + 1,
					"total_retries": attempt,
				}).Info("Function succeeded after retries")
			}
			return nil
		}

		lastErr = err

		// Check if error is retryable
		if retryableErr, ok := err.(*RetryableError); ok && !retryableErr.Retryable {
			logger.WithError(err).Debug("Error is not retryable, stopping")
			return err
		}

		// Don't retry on the last attempt
		if attempt == cfg.Retry.MaxRetries {
			break
		}

		// Check if context is cancelled
		if ctx.Err() != nil {
			return ctx.Err()
		}

		// Log retry attempt
		logger.WithFields(logrus.Fields{
			"attempt":    attempt + 1,
			"max_retries": cfg.Retry.MaxRetries,
			"delay":      delay,
			"error":      err.Error(),
		}).Warn("Function failed, retrying")

		// Wait with jitter
		jitteredDelay := addJitter(delay, cfg.Retry.Jitter)
		
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(jitteredDelay):
		}

		// Calculate next delay with exponential backoff
		delay = time.Duration(float64(delay) * cfg.Retry.BackoffMultiplier)
		if delay > cfg.Retry.MaxDelay {
			delay = cfg.Retry.MaxDelay
		}
	}

	logger.WithFields(logrus.Fields{
		"max_retries": cfg.Retry.MaxRetries,
		"final_error": lastErr.Error(),
	}).Error("Function failed after all retry attempts")

	return fmt.Errorf("function failed after %d retries: %w", cfg.Retry.MaxRetries, lastErr)
}

// addJitter adds random jitter to a delay
func addJitter(delay time.Duration, jitterFactor float64) time.Duration {
	if jitterFactor <= 0 {
		return delay
	}

	// Add random jitter up to jitterFactor percentage
	jitter := time.Duration(rand.Float64() * float64(delay) * jitterFactor)
	return delay + jitter
}

// IsRetryableError checks if an error should be retried
func IsRetryableError(err error) bool {
	if err == nil {
		return false
	}

	errorStr := err.Error()
	
	// Network-related errors that are typically retryable
	retryableErrors := []string{
		"timeout",
		"connection refused",
		"connection reset",
		"temporary failure",
		"service unavailable",
		"too many requests",
		"rate limit",
		"network is unreachable",
		"no route to host",
		"context deadline exceeded",
	}

	for _, retryableError := range retryableErrors {
		if contains(errorStr, retryableError) {
			return true
		}
	}

	return false
}

// contains checks if a string contains a substring (case-insensitive)
func contains(s, substr string) bool {
	return len(s) >= len(substr) && 
		   (s == substr || 
		    len(s) > len(substr) && 
		    (s[:len(substr)] == substr || 
		     s[len(s)-len(substr):] == substr || 
		     containsSubstring(s, substr)))
}

func containsSubstring(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}

// RetryableHTTPError creates a retryable error for HTTP status codes
func RetryableHTTPError(statusCode int, message string) *RetryableError {
	retryable := false
	
	// HTTP status codes that are typically retryable
	switch statusCode {
	case 408, 429, 500, 502, 503, 504:
		retryable = true
	}

	return NewRetryableError(fmt.Errorf("HTTP %d: %s", statusCode, message), retryable)
}

// CircuitBreaker implements a simple circuit breaker pattern
type CircuitBreaker struct {
	maxFailures   int
	resetTimeout  time.Duration
	failures      int
	lastFailTime  time.Time
	state         CircuitState
	logger        *logrus.Logger
}

// CircuitState represents the state of a circuit breaker
type CircuitState int

const (
	CircuitClosed CircuitState = iota
	CircuitOpen
	CircuitHalfOpen
)

// NewCircuitBreaker creates a new circuit breaker
func NewCircuitBreaker(maxFailures int, resetTimeout time.Duration, logger *logrus.Logger) *CircuitBreaker {
	return &CircuitBreaker{
		maxFailures:  maxFailures,
		resetTimeout: resetTimeout,
		state:        CircuitClosed,
		logger:       logger,
	}
}

// Execute executes a function with circuit breaker protection
func (cb *CircuitBreaker) Execute(fn func() error) error {
	if cb.state == CircuitOpen {
		if time.Since(cb.lastFailTime) > cb.resetTimeout {
			cb.state = CircuitHalfOpen
			cb.logger.Debug("Circuit breaker transitioning to half-open state")
		} else {
			return fmt.Errorf("circuit breaker is open")
		}
	}

	err := fn()
	
	if err != nil {
		cb.onFailure()
		return err
	}

	cb.onSuccess()
	return nil
}

// onSuccess handles successful execution
func (cb *CircuitBreaker) onSuccess() {
	cb.failures = 0
	if cb.state == CircuitHalfOpen {
		cb.state = CircuitClosed
		cb.logger.Debug("Circuit breaker closed after successful execution")
	}
}

// onFailure handles failed execution
func (cb *CircuitBreaker) onFailure() {
	cb.failures++
	cb.lastFailTime = time.Now()

	if cb.failures >= cb.maxFailures {
		cb.state = CircuitOpen
		cb.logger.WithFields(logrus.Fields{
			"failures":      cb.failures,
			"max_failures":  cb.maxFailures,
			"reset_timeout": cb.resetTimeout,
		}).Warn("Circuit breaker opened due to failures")
	}
}

// GetState returns the current state of the circuit breaker
func (cb *CircuitBreaker) GetState() CircuitState {
	return cb.state
}

// Reset resets the circuit breaker to closed state
func (cb *CircuitBreaker) Reset() {
	cb.failures = 0
	cb.state = CircuitClosed
	cb.logger.Debug("Circuit breaker manually reset")
}

// ExponentialBackoff calculates delay using exponential backoff
func ExponentialBackoff(attempt int, baseDelay time.Duration, maxDelay time.Duration, multiplier float64) time.Duration {
	delay := time.Duration(float64(baseDelay) * math.Pow(multiplier, float64(attempt)))
	if delay > maxDelay {
		delay = maxDelay
	}
	return delay
}
