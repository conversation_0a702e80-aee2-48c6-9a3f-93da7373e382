//go:build integration
// +build integration

package test

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"aistudio-proxy/internal/aistudio"
	"aistudio-proxy/internal/session"
)

const (
	baseURL = "http://localhost:8080"
	apiKey  = "test-api-key"
)

// TestIntegrationHealthCheck tests the health endpoint
func TestIntegrationHealthCheck(t *testing.T) {
	resp, err := http.Get(baseURL + "/health")
	require.NoError(t, err)
	defer resp.Body.Close()

	assert.Equal(t, http.StatusOK, resp.StatusCode)

	var health map[string]interface{}
	err = json.NewDecoder(resp.Body).Decode(&health)
	require.NoError(t, err)

	assert.Equal(t, "healthy", health["status"])
	assert.Contains(t, health, "version")
	assert.Contains(t, health, "uptime")
	assert.Contains(t, health, "active_sessions")
}

// TestIntegrationSessionLifecycle tests session creation, usage, and deletion
func TestIntegrationSessionLifecycle(t *testing.T) {
	client := &http.Client{Timeout: 30 * time.Second}

	// 1. Create session
	sessionReq := session.CreateSessionRequest{
		Model:      "gemini-pro",
		TTLMinutes: 30,
	}
	reqBody, _ := json.Marshal(sessionReq)

	req, _ := http.NewRequest("POST", baseURL+"/api/v1/session", bytes.NewBuffer(reqBody))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+apiKey)

	resp, err := client.Do(req)
	require.NoError(t, err)
	defer resp.Body.Close()

	assert.Equal(t, http.StatusOK, resp.StatusCode)

	var sessionResp session.CreateSessionResponse
	err = json.NewDecoder(resp.Body).Decode(&sessionResp)
	require.NoError(t, err)
	require.NotEmpty(t, sessionResp.SessionID)

	sessionID := sessionResp.SessionID

	// 2. Use session for chat (if AI Studio is available)
	if os.Getenv("AI_STUDIO_EMAIL") != "" {
		chatReq := aistudio.ChatRequest{
			Message:   "Hello, this is a test message",
			SessionID: sessionID,
		}
		chatBody, _ := json.Marshal(chatReq)

		chatReqHTTP, _ := http.NewRequest("POST", baseURL+"/api/v1/chat", bytes.NewBuffer(chatBody))
		chatReqHTTP.Header.Set("Content-Type", "application/json")
		chatReqHTTP.Header.Set("Authorization", "Bearer "+apiKey)

		chatResp, err := client.Do(chatReqHTTP)
		require.NoError(t, err)
		defer chatResp.Body.Close()

		// Should succeed or fail gracefully
		assert.True(t, chatResp.StatusCode == http.StatusOK || chatResp.StatusCode >= 400)
	}

	// 3. Delete session
	delReq, _ := http.NewRequest("DELETE", baseURL+"/api/v1/session/"+sessionID, nil)
	delReq.Header.Set("Authorization", "Bearer "+apiKey)

	delResp, err := client.Do(delReq)
	require.NoError(t, err)
	defer delResp.Body.Close()

	assert.Equal(t, http.StatusOK, delResp.StatusCode)

	var delResult map[string]string
	err = json.NewDecoder(delResp.Body).Decode(&delResult)
	require.NoError(t, err)
	assert.Equal(t, "deleted", delResult["status"])
}

// TestIntegrationAuthenticationFailure tests authentication failures
func TestIntegrationAuthenticationFailure(t *testing.T) {
	client := &http.Client{Timeout: 10 * time.Second}

	// Test without auth header
	req, _ := http.NewRequest("GET", baseURL+"/api/v1/sessions", nil)
	resp, err := client.Do(req)
	require.NoError(t, err)
	defer resp.Body.Close()

	assert.Equal(t, http.StatusUnauthorized, resp.StatusCode)

	// Test with invalid auth header
	req, _ = http.NewRequest("GET", baseURL+"/api/v1/sessions", nil)
	req.Header.Set("Authorization", "Bearer invalid-key")
	resp, err = client.Do(req)
	require.NoError(t, err)
	defer resp.Body.Close()

	assert.Equal(t, http.StatusUnauthorized, resp.StatusCode)
}

// TestIntegrationRateLimit tests rate limiting (if enabled)
func TestIntegrationRateLimit(t *testing.T) {
	client := &http.Client{Timeout: 5 * time.Second}

	// Make multiple rapid requests
	for i := 0; i < 50; i++ {
		req, _ := http.NewRequest("GET", baseURL+"/health", nil)
		resp, err := client.Do(req)
		require.NoError(t, err)
		resp.Body.Close()

		// If rate limiting is enabled, we should eventually get 429
		if resp.StatusCode == http.StatusTooManyRequests {
			t.Logf("Rate limit triggered after %d requests", i+1)
			return
		}

		// Small delay to avoid overwhelming the server
		time.Sleep(10 * time.Millisecond)
	}

	t.Log("Rate limiting not triggered or not enabled")
}

// TestIntegrationModelsEndpoint tests the models endpoint
func TestIntegrationModelsEndpoint(t *testing.T) {
	client := &http.Client{Timeout: 30 * time.Second}

	req, _ := http.NewRequest("GET", baseURL+"/api/v1/models", nil)
	req.Header.Set("Authorization", "Bearer "+apiKey)

	resp, err := client.Do(req)
	require.NoError(t, err)
	defer resp.Body.Close()

	// Should succeed or fail gracefully
	assert.True(t, resp.StatusCode == http.StatusOK || resp.StatusCode >= 400)

	if resp.StatusCode == http.StatusOK {
		var models []aistudio.Model
		err = json.NewDecoder(resp.Body).Decode(&models)
		require.NoError(t, err)
		assert.NotEmpty(t, models)

		// Check model structure
		for _, model := range models {
			assert.NotEmpty(t, model.ID)
			assert.NotEmpty(t, model.Name)
			assert.NotEmpty(t, model.Description)
			assert.NotEmpty(t, model.Capabilities)
		}
	}
}

// TestIntegrationConcurrentRequests tests concurrent request handling
func TestIntegrationConcurrentRequests(t *testing.T) {
	client := &http.Client{Timeout: 10 * time.Second}
	numRequests := 10

	// Channel to collect results
	results := make(chan error, numRequests)

	// Launch concurrent requests
	for i := 0; i < numRequests; i++ {
		go func(id int) {
			req, _ := http.NewRequest("GET", baseURL+"/health", nil)
			resp, err := client.Do(req)
			if err != nil {
				results <- fmt.Errorf("request %d failed: %w", id, err)
				return
			}
			defer resp.Body.Close()

			if resp.StatusCode != http.StatusOK {
				results <- fmt.Errorf("request %d returned status %d", id, resp.StatusCode)
				return
			}

			results <- nil
		}(i)
	}

	// Collect results
	var errors []error
	for i := 0; i < numRequests; i++ {
		if err := <-results; err != nil {
			errors = append(errors, err)
		}
	}

	// Assert all requests succeeded
	if len(errors) > 0 {
		for _, err := range errors {
			t.Logf("Error: %v", err)
		}
		t.Fatalf("Failed %d out of %d concurrent requests", len(errors), numRequests)
	}

	t.Logf("All %d concurrent requests succeeded", numRequests)
}

// TestIntegrationMetricsEndpoint tests the metrics endpoint
func TestIntegrationMetricsEndpoint(t *testing.T) {
	resp, err := http.Get(baseURL + "/metrics")
	require.NoError(t, err)
	defer resp.Body.Close()

	assert.Equal(t, http.StatusOK, resp.StatusCode)
	assert.Equal(t, "text/plain", resp.Header.Get("Content-Type"))

	// Read response body
	buf := new(bytes.Buffer)
	_, err = buf.ReadFrom(resp.Body)
	require.NoError(t, err)

	metrics := buf.String()
	assert.Contains(t, metrics, "aistudio_proxy_sessions_active")
	assert.Contains(t, metrics, "aistudio_proxy_browser_contexts_active")
}
