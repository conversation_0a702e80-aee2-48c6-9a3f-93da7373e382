@echo off
echo Building AI Studio Proxy...

echo Step 1: Cleaning module cache...
go clean -modcache

echo Step 2: Downloading dependencies...
go mod download

echo Step 3: Tidying modules...
go mod tidy

echo Step 4: Building application...
go build -o bin\aistudio-proxy.exe cmd\server\main.go

echo Step 5: Running basic tests...
go test -v ./internal/config
go test -v ./internal/session

echo Build complete!
if exist bin\aistudio-proxy.exe (
    echo Binary created successfully: bin\aistudio-proxy.exe
) else (
    echo Build failed - binary not found
    exit /b 1
)
