version: '3.8'

services:
  aistudio-proxy:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        VERSION: ${VERSION:-dev}
        BUILD_TIME: ${BUILD_TIME}
        GIT_COMMIT: ${GIT_COMMIT}
    container_name: aistudio-proxy
    ports:
      - "${PORT:-8080}:8080"
    environment:
      # Server Configuration
      - PORT=8080
      - HOST=0.0.0.0
      
      # Authentication (REQUIRED)
      - API_KEYS=${API_KEYS:-your-api-key-here}
      - AUTH_ENABLED=${AUTH_ENABLED:-true}
      
      # AI Studio Configuration (REQUIRED)
      - AI_STUDIO_EMAIL=${AI_STUDIO_EMAIL}
      - AI_STUDIO_PASSWORD=${AI_STUDIO_PASSWORD}
      - AI_STUDIO_DEFAULT_MODEL=${AI_STUDIO_DEFAULT_MODEL:-gemini-pro}
      
      # Browser Configuration
      - BROWSER_TYPE=${BROWSER_TYPE:-chrome}
      - BROWSER_PROFILE_PATH=/app/profiles
      - HEADLESS=${HEADLESS:-true}
      
      # Session Configuration
      - SESSION_TTL_MINUTES=${SESSION_TTL_MINUTES:-60}
      
      # Logging Configuration
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - LOG_FORMAT=${LOG_FORMAT:-json}
      
      # Development Configuration
      - DEVELOPMENT_MODE=${DEVELOPMENT_MODE:-false}
      - MOCK_RESPONSES=${MOCK_RESPONSES:-false}
    volumes:
      # Browser profiles persistence
      - browser-profiles:/app/profiles
      # Logs persistence
      - logs:/app/logs
      # Configuration file (optional)
      - ./config.yaml:/app/config.yaml:ro
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - aistudio-proxy-network
    # Resource limits
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'

  # Optional: Prometheus for metrics collection
  prometheus:
    image: prom/prometheus:latest
    container_name: aistudio-proxy-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./docker/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - aistudio-proxy-network
    profiles:
      - monitoring

  # Optional: Grafana for metrics visualization
  grafana:
    image: grafana/grafana:latest
    container_name: aistudio-proxy-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
    volumes:
      - grafana-data:/var/lib/grafana
      - ./docker/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./docker/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    networks:
      - aistudio-proxy-network
    profiles:
      - monitoring
    depends_on:
      - prometheus

  # Optional: Redis for session persistence (future enhancement)
  redis:
    image: redis:7-alpine
    container_name: aistudio-proxy-redis
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    command: redis-server --appendonly yes
    networks:
      - aistudio-proxy-network
    profiles:
      - persistence

volumes:
  browser-profiles:
    driver: local
  logs:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local
  redis-data:
    driver: local

networks:
  aistudio-proxy-network:
    driver: bridge
