package api

import (
	"context"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"

	"aistudio-proxy/internal/aistudio"
	"aistudio-proxy/internal/browser"
	"aistudio-proxy/internal/config"
	"aistudio-proxy/internal/session"
)

// Handlers contains all HTTP handlers
type Handlers struct {
	config         *config.Config
	logger         *logrus.Logger
	browserManager *browser.Manager
	sessionManager *session.Manager
	aiStudioClient *aistudio.Client
}

// ErrorResponse represents an error response
type ErrorResponse struct {
	Error APIError `json:"error"`
}

// APIError represents an API error
type APIError struct {
	Code    string      `json:"code"`
	Message string      `json:"message"`
	Details interface{} `json:"details,omitempty"`
}

// HealthResponse represents a health check response
type HealthResponse struct {
	Status         string `json:"status"`
	Version        string `json:"version"`
	Uptime         int64  `json:"uptime"`
	ActiveSessions int    `json:"active_sessions"`
}

// NewHandlers creates new API handlers
func NewHandlers(cfg *config.Config, logger *logrus.Logger, browserManager *browser.Manager, sessionManager *session.Manager) *Handlers {
	aiStudioClient := aistudio.NewClient(cfg, logger, browserManager)

	return &Handlers{
		config:         cfg,
		logger:         logger,
		browserManager: browserManager,
		sessionManager: sessionManager,
		aiStudioClient: aiStudioClient,
	}
}

// RegisterRoutes registers all API routes
func (h *Handlers) RegisterRoutes(router *gin.RouterGroup) {
	// Chat endpoints
	router.POST("/chat", h.Chat)
	router.POST("/chat/stream", h.ChatStream)

	// Session endpoints
	router.POST("/session", h.CreateSession)
	router.DELETE("/session/:id", h.DeleteSession)
	router.PATCH("/session/:id", h.RenewSession)
	router.GET("/sessions", h.ListSessions)

	// Model endpoints
	router.GET("/models", h.GetModels)
}

// Chat handles chat requests
// @Summary Send a message to AI Studio
// @Description Send a message and optional file to AI Studio and get a response
// @Tags chat
// @Accept json,multipart/form-data
// @Produce json
// @Param request body aistudio.ChatRequest true "Chat request"
// @Success 200 {object} aistudio.ChatResponse
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 429 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Security BearerAuth
// @Router /chat [post]
func (h *Handlers) Chat(c *gin.Context) {
	requestID := GetRequestID(c)
	logger := h.logger.WithField("request_id", requestID)

	var req aistudio.ChatRequest

	// Handle multipart form data for file uploads
	if strings.Contains(c.GetHeader("Content-Type"), "multipart/form-data") {
		if err := h.parseMultipartRequest(c, &req); err != nil {
			logger.WithError(err).Error("Failed to parse multipart request")
			h.respondWithError(c, http.StatusBadRequest, "INVALID_REQUEST", "Failed to parse request", err)
			return
		}
	} else {
		// Handle JSON request
		if err := c.ShouldBindJSON(&req); err != nil {
			logger.WithError(err).Error("Failed to bind JSON request")
			h.respondWithError(c, http.StatusBadRequest, "INVALID_REQUEST", "Invalid JSON request", err)
			return
		}
	}

	// Validate request
	if err := h.validateChatRequest(&req); err != nil {
		logger.WithError(err).Error("Request validation failed")
		h.respondWithError(c, http.StatusBadRequest, "VALIDATION_ERROR", "Request validation failed", err)
		return
	}

	// Get or create session
	sessionReq := &session.CreateSessionRequest{
		Model: req.Model,
	}

	sess, created, err := h.sessionManager.GetOrCreateSession(req.SessionID, sessionReq)
	if err != nil {
		logger.WithError(err).Error("Failed to get or create session")
		h.respondWithError(c, http.StatusInternalServerError, "SESSION_ERROR", "Failed to manage session", err)
		return
	}

	// Update request with session ID
	req.SessionID = sess.ID

	if created {
		logger.WithField("session_id", sess.ID).Info("Created new session for chat request")
	}

	// Create context with timeout
	ctx, cancel := context.WithTimeout(c.Request.Context(), h.config.AIStudio.RequestTimeout)
	defer cancel()

	// Send message to AI Studio
	response, err := h.aiStudioClient.SendMessage(ctx, &req)
	if err != nil {
		logger.WithError(err).Error("Failed to send message to AI Studio")

		// Determine error type and status code
		statusCode := http.StatusInternalServerError
		errorCode := "AI_STUDIO_ERROR"

		if strings.Contains(err.Error(), "authentication") {
			statusCode = http.StatusUnauthorized
			errorCode = "AUTHENTICATION_ERROR"
		} else if strings.Contains(err.Error(), "rate limit") {
			statusCode = http.StatusTooManyRequests
			errorCode = "RATE_LIMIT_EXCEEDED"
		} else if strings.Contains(err.Error(), "timeout") {
			statusCode = http.StatusRequestTimeout
			errorCode = "REQUEST_TIMEOUT"
		}

		h.respondWithError(c, statusCode, errorCode, "Failed to get response from AI Studio", err)
		return
	}

	logger.WithFields(logrus.Fields{
		"session_id":      response.SessionID,
		"model_used":      response.ModelUsed,
		"response_length": len(response.Response),
	}).Info("Successfully processed chat request")

	c.Header("X-Request-ID", requestID)
	c.JSON(http.StatusOK, response)
}

// ChatStream handles streaming chat requests
// @Summary Send a streaming message to AI Studio
// @Description Send a message and get a streaming response via Server-Sent Events
// @Tags chat
// @Accept json
// @Produce text/event-stream
// @Param request body aistudio.ChatRequest true "Chat request"
// @Success 200 {string} string "Server-Sent Events stream"
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 429 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Security BearerAuth
// @Router /chat/stream [post]
func (h *Handlers) ChatStream(c *gin.Context) {
	// For now, redirect to regular chat endpoint
	// Streaming implementation would require more complex response handling
	h.Chat(c)
}

// CreateSession creates a new session
// @Summary Create a new AI Studio session
// @Description Initialize a new session with specified model and TTL
// @Tags session
// @Accept json
// @Produce json
// @Param request body session.CreateSessionRequest true "Session creation request"
// @Success 200 {object} session.CreateSessionResponse
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Security BearerAuth
// @Router /session [post]
func (h *Handlers) CreateSession(c *gin.Context) {
	requestID := GetRequestID(c)
	logger := h.logger.WithField("request_id", requestID)

	var req session.CreateSessionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.WithError(err).Error("Failed to bind JSON request")
		h.respondWithError(c, http.StatusBadRequest, "INVALID_REQUEST", "Invalid JSON request", err)
		return
	}

	// Create session
	response, err := h.sessionManager.CreateSession(&req)
	if err != nil {
		logger.WithError(err).Error("Failed to create session")

		statusCode := http.StatusInternalServerError
		errorCode := "SESSION_ERROR"

		if strings.Contains(err.Error(), "maximum number") {
			statusCode = http.StatusServiceUnavailable
			errorCode = "MAX_SESSIONS_REACHED"
		}

		h.respondWithError(c, statusCode, errorCode, "Failed to create session", err)
		return
	}

	logger.WithFields(logrus.Fields{
		"session_id": response.SessionID,
		"model":      response.Model,
		"expires_at": response.ExpiresAt,
	}).Info("Created new session")

	c.Header("X-Request-ID", requestID)
	c.JSON(http.StatusOK, response)
}

// DeleteSession deletes a session
// @Summary Delete a session
// @Description Clean up a specific session and its resources
// @Tags session
// @Produce json
// @Param id path string true "Session ID"
// @Success 200 {object} map[string]string
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Security BearerAuth
// @Router /session/{id} [delete]
func (h *Handlers) DeleteSession(c *gin.Context) {
	requestID := GetRequestID(c)
	logger := h.logger.WithField("request_id", requestID)

	sessionID := c.Param("id")
	if sessionID == "" {
		h.respondWithError(c, http.StatusBadRequest, "INVALID_REQUEST", "Session ID is required", nil)
		return
	}

	// Validate session ID format
	if _, err := uuid.Parse(sessionID); err != nil {
		h.respondWithError(c, http.StatusBadRequest, "INVALID_SESSION_ID", "Invalid session ID format", err)
		return
	}

	// Delete session
	err := h.sessionManager.DeleteSession(sessionID)
	if err != nil {
		logger.WithError(err).Error("Failed to delete session")

		statusCode := http.StatusInternalServerError
		errorCode := "SESSION_ERROR"

		if strings.Contains(err.Error(), "not found") {
			statusCode = http.StatusNotFound
			errorCode = "SESSION_NOT_FOUND"
		}

		h.respondWithError(c, statusCode, errorCode, "Failed to delete session", err)
		return
	}

	// Also remove browser context
	if err := h.browserManager.RemoveContext(sessionID); err != nil {
		logger.WithError(err).Warn("Failed to remove browser context")
	}

	logger.WithField("session_id", sessionID).Info("Deleted session")

	c.Header("X-Request-ID", requestID)
	c.JSON(http.StatusOK, gin.H{
		"status": "deleted",
	})
}

// RenewSession renews a session's TTL
// @Summary Renew a session
// @Description Extend a session's time-to-live
// @Tags session
// @Accept json
// @Produce json
// @Param id path string true "Session ID"
// @Param request body map[string]int true "Renewal request with ttl_minutes"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Security BearerAuth
// @Router /session/{id} [patch]
func (h *Handlers) RenewSession(c *gin.Context) {
	requestID := GetRequestID(c)
	logger := h.logger.WithField("request_id", requestID)

	sessionID := c.Param("id")
	if sessionID == "" {
		h.respondWithError(c, http.StatusBadRequest, "INVALID_REQUEST", "Session ID is required", nil)
		return
	}

	// Validate session ID format
	if _, err := uuid.Parse(sessionID); err != nil {
		h.respondWithError(c, http.StatusBadRequest, "INVALID_SESSION_ID", "Invalid session ID format", err)
		return
	}

	var req struct {
		TTLMinutes int `json:"ttl_minutes"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		logger.WithError(err).Error("Failed to bind JSON request")
		h.respondWithError(c, http.StatusBadRequest, "INVALID_REQUEST", "Invalid JSON request", err)
		return
	}

	// Renew session
	err := h.sessionManager.RenewSession(sessionID, req.TTLMinutes)
	if err != nil {
		logger.WithError(err).Error("Failed to renew session")

		statusCode := http.StatusInternalServerError
		errorCode := "SESSION_ERROR"

		if strings.Contains(err.Error(), "not found") || strings.Contains(err.Error(), "expired") {
			statusCode = http.StatusNotFound
			errorCode = "SESSION_NOT_FOUND"
		}

		h.respondWithError(c, statusCode, errorCode, "Failed to renew session", err)
		return
	}

	// Get updated session info
	session, err := h.sessionManager.GetSession(sessionID)
	if err != nil {
		logger.WithError(err).Error("Failed to get renewed session")
		h.respondWithError(c, http.StatusInternalServerError, "SESSION_ERROR", "Failed to get session info", err)
		return
	}

	logger.WithFields(logrus.Fields{
		"session_id": sessionID,
		"expires_at": session.ExpiresAt,
	}).Info("Renewed session")

	c.Header("X-Request-ID", requestID)
	c.JSON(http.StatusOK, gin.H{
		"status":     "renewed",
		"expires_at": session.ExpiresAt,
	})
}

// ListSessions lists all active sessions
// @Summary List active sessions
// @Description Get information about all active sessions
// @Tags session
// @Produce json
// @Success 200 {array} session.SessionInfo
// @Failure 500 {object} ErrorResponse
// @Security BearerAuth
// @Router /sessions [get]
func (h *Handlers) ListSessions(c *gin.Context) {
	requestID := GetRequestID(c)
	logger := h.logger.WithField("request_id", requestID)

	sessions, err := h.sessionManager.ListSessions()
	if err != nil {
		logger.WithError(err).Error("Failed to list sessions")
		h.respondWithError(c, http.StatusInternalServerError, "SESSION_ERROR", "Failed to list sessions", err)
		return
	}

	c.Header("X-Request-ID", requestID)
	c.JSON(http.StatusOK, sessions)
}

// GetModels returns available AI Studio models
// @Summary Get available models
// @Description List all available AI Studio models and their capabilities
// @Tags models
// @Produce json
// @Success 200 {array} aistudio.Model
// @Failure 500 {object} ErrorResponse
// @Security BearerAuth
// @Router /models [get]
func (h *Handlers) GetModels(c *gin.Context) {
	requestID := GetRequestID(c)
	logger := h.logger.WithField("request_id", requestID)

	// Create a temporary session for model discovery
	sessionReq := &session.CreateSessionRequest{}
	sess, _, err := h.sessionManager.GetOrCreateSession("", sessionReq)
	if err != nil {
		logger.WithError(err).Error("Failed to create session for model discovery")
		h.respondWithError(c, http.StatusInternalServerError, "SESSION_ERROR", "Failed to create session", err)
		return
	}
	defer h.sessionManager.DeleteSession(sess.ID)

	// Create context with timeout
	ctx, cancel := context.WithTimeout(c.Request.Context(), 30*time.Second)
	defer cancel()

	// Get available models
	models, err := h.aiStudioClient.GetAvailableModels(ctx, sess.ID)
	if err != nil {
		logger.WithError(err).Error("Failed to get available models")
		h.respondWithError(c, http.StatusInternalServerError, "AI_STUDIO_ERROR", "Failed to get models", err)
		return
	}

	c.Header("X-Request-ID", requestID)
	c.JSON(http.StatusOK, models)
}

// HealthCheck performs a health check
// @Summary Health check
// @Description Check the health status of the service
// @Tags health
// @Produce json
// @Success 200 {object} HealthResponse
// @Failure 503 {object} ErrorResponse
// @Router /health [get]
func (h *Handlers) HealthCheck(c *gin.Context) {
	requestID := GetRequestID(c)

	status := "healthy"
	statusCode := http.StatusOK

	// Check browser manager
	if h.browserManager.GetContextCount() < 0 {
		status = "unhealthy"
		statusCode = http.StatusServiceUnavailable
	}

	// Detailed health check if enabled
	if h.config.Health.Detailed {
		// Create a temporary session for connection test
		sessionReq := &session.CreateSessionRequest{}
		sess, _, err := h.sessionManager.GetOrCreateSession("", sessionReq)
		if err == nil {
			// Test AI Studio connection
			ctx, cancel := context.WithTimeout(c.Request.Context(), h.config.Health.Timeout)
			defer cancel()

			if err := h.aiStudioClient.ValidateConnection(ctx, sess.ID); err != nil {
				h.logger.WithError(err).Warn("AI Studio connection check failed")
				status = "unhealthy"
				statusCode = http.StatusServiceUnavailable
			}

			h.sessionManager.DeleteSession(sess.ID)
		}
	}

	response := HealthResponse{
		Status:         status,
		Version:        "1.0.0",                                 // This would come from build info
		Uptime:         int64(time.Since(time.Now()).Seconds()), // This would be actual uptime
		ActiveSessions: h.sessionManager.GetSessionCount(),
	}

	c.Header("X-Request-ID", requestID)
	c.JSON(statusCode, response)
}

// Metrics returns Prometheus metrics
func (h *Handlers) Metrics(c *gin.Context) {
	// This would integrate with Prometheus metrics
	// For now, return basic metrics in text format
	metrics := fmt.Sprintf(`# HELP aistudio_proxy_sessions_active Number of active sessions
# TYPE aistudio_proxy_sessions_active gauge
aistudio_proxy_sessions_active %d

# HELP aistudio_proxy_browser_contexts_active Number of active browser contexts
# TYPE aistudio_proxy_browser_contexts_active gauge
aistudio_proxy_browser_contexts_active %d
`, h.sessionManager.GetSessionCount(), h.browserManager.GetContextCount())

	c.Header("Content-Type", "text/plain")
	c.String(http.StatusOK, metrics)
}
