# AI Studio Proxy

A production-ready proxy server that provides programmatic access to Google AI Studio's chat functionality through a clean REST API interface. Built with <PERSON> and Playwright for reliable browser automation.

## Features

- 🤖 **AI Studio Integration**: Seamless integration with Google AI Studio's web interface
- 🌐 **REST API**: Clean, well-documented REST API with OpenAPI 3.0 specification
- 🔒 **Authentication**: API key-based authentication with session management
- 📁 **File Uploads**: Support for images, documents, and other file types
- 🔄 **Session Management**: Persistent sessions with configurable TTL
- 🚀 **High Performance**: Connection pooling and concurrent request handling
- 📊 **Observability**: Structured logging, metrics, and health checks
- 🛡️ **Security**: Rate limiting, CORS, input sanitization, and security headers
- 🐳 **Docker Ready**: Multi-stage Docker build with docker-compose support
- 🔧 **Configurable**: Environment variables and YAML configuration support

## Prerequisites

- Go 1.21 or higher
- Google account with access to AI Studio
- Chrome, Chromium, or Opera browser (for profile-based authentication)

## Quick Start

### 1. Clone the Repository

```bash
git clone <repository-url>
cd aistudio-proxy
```

### 2. Set Up Environment Variables

Create a `.env` file or set environment variables:

```bash
# Required Configuration
export API_KEYS="your-secret-api-key-1,your-secret-api-key-2"
export AI_STUDIO_EMAIL="<EMAIL>"
export AI_STUDIO_PASSWORD="your-password"

# Optional Configuration
export PORT=8080
export LOG_LEVEL=INFO
export HEADLESS=true
export BROWSER_PROFILE_PATH="./profiles"
```

### 3. Install Dependencies and Run

```bash
# Set up project and install dependencies
make setup

# Run the server
make run
```

### 4. Test the API

```bash
# Health check
curl http://localhost:8080/health

# Send a chat message
curl -X POST http://localhost:8080/api/v1/chat \
  -H "Authorization: Bearer your-secret-api-key-1" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "Hello, how are you today?",
    "model": "gemini-pro"
  }'
```

## API Documentation

### Authentication

All API endpoints (except `/health`) require authentication using Bearer tokens:

```bash
Authorization: Bearer your-api-key
```

### Core Endpoints

#### POST /api/v1/chat
Send a message to AI Studio and get a response.

**Request:**
```json
{
  "message": "Hello, how are you?",
  "model": "gemini-pro",
  "session_id": "optional-session-id",
  "file": {
    "name": "document.pdf",
    "content": "base64-encoded-content",
    "mime_type": "application/pdf"
  }
}
```

**Response:**
```json
{
  "response": "Hello! I'm doing well, thank you for asking.",
  "session_id": "550e8400-e29b-41d4-a716-446655440000",
  "model_used": "gemini-pro",
  "timestamp": "2023-12-07T10:30:00Z",
  "status": "success"
}
```

#### POST /api/v1/session
Create a new session.

**Request:**
```json
{
  "model": "gemini-pro",
  "ttl_minutes": 60
}
```

**Response:**
```json
{
  "session_id": "550e8400-e29b-41d4-a716-446655440000",
  "expires_at": "2023-12-07T11:30:00Z",
  "model": "gemini-pro"
}
```

#### GET /api/v1/models
List available AI Studio models.

**Response:**
```json
[
  {
    "id": "gemini-pro",
    "name": "Gemini Pro",
    "description": "Most capable model for complex reasoning tasks",
    "capabilities": ["text", "images", "documents"]
  }
]
```

#### GET /health
Health check endpoint (no authentication required).

**Response:**
```json
{
  "status": "healthy",
  "version": "1.0.0",
  "uptime": 3600,
  "active_sessions": 5
}
```

### File Upload

Files can be uploaded in two ways:

1. **JSON with base64 encoding:**
```json
{
  "message": "Analyze this image",
  "file": {
    "name": "image.jpg",
    "content": "base64-encoded-content",
    "mime_type": "image/jpeg"
  }
}
```

2. **Multipart form data:**
```bash
curl -X POST http://localhost:8080/api/v1/chat \
  -H "Authorization: Bearer your-api-key" \
  -F "message=Analyze this image" \
  -F "file=@image.jpg"
```

## Configuration

### Environment Variables

| Variable | Required | Default | Description |
|----------|----------|---------|-------------|
| `API_KEYS` | Yes | - | Comma-separated API keys |
| `AI_STUDIO_EMAIL` | Yes | - | Google account email |
| `AI_STUDIO_PASSWORD` | Yes | - | Google account password |
| `PORT` | No | 8080 | Server port |
| `HOST` | No | 0.0.0.0 | Server host |
| `LOG_LEVEL` | No | INFO | Log level (DEBUG, INFO, WARN, ERROR) |
| `HEADLESS` | No | true | Run browser in headless mode |
| `BROWSER_PROFILE_PATH` | No | - | Path to browser profile directory |
| `SESSION_TTL_MINUTES` | No | 60 | Default session TTL in minutes |

### Configuration File

You can also use a `config.yaml` file. Copy `config.yaml.example` to `config.yaml` and customize:

```yaml
server:
  port: 8080
  host: "0.0.0.0"

auth:
  api_keys: ["your-api-key-1", "your-api-key-2"]
  enabled: true

aistudio:
  email: "<EMAIL>"
  password: "your-password"
  default_model: "gemini-pro"

browser:
  type: "chrome"
  headless: true
  profile_path: "./profiles"
```

## Docker Deployment

### Using Docker Compose (Recommended)

1. **Create environment file:**
```bash
cp .env.example .env
# Edit .env with your configuration
```

2. **Start the service:**
```bash
docker-compose up -d
```

3. **With monitoring (optional):**
```bash
docker-compose --profile monitoring up -d
```

### Using Docker directly

```bash
# Build the image
docker build -t aistudio-proxy .

# Run the container
docker run -d \
  --name aistudio-proxy \
  -p 8080:8080 \
  -e API_KEYS="your-api-key" \
  -e AI_STUDIO_EMAIL="<EMAIL>" \
  -e AI_STUDIO_PASSWORD="your-password" \
  -v $(pwd)/profiles:/app/profiles \
  aistudio-proxy
```

## Development

### Building from Source

```bash
# Install dependencies
make install-deps

# Build the application
make build

# Run tests
make test

# Generate API documentation
make generate-docs

# Format and lint code
make fmt
make lint
```

### Development Mode

```bash
# Run in development mode with debug logging
make dev

# Or set environment variables
HEADLESS=false LOG_LEVEL=DEBUG go run cmd/server/main.go
```

## Monitoring and Observability

### Metrics

Prometheus metrics are available at `/metrics`:

```bash
curl http://localhost:8080/metrics
```

Key metrics:
- `aistudio_proxy_sessions_active`: Number of active sessions
- `aistudio_proxy_browser_contexts_active`: Number of active browser contexts

### Logging

Structured JSON logging with configurable levels:

```json
{
  "level": "info",
  "msg": "Successfully processed chat request",
  "request_id": "550e8400-e29b-41d4-a716-446655440000",
  "session_id": "123e4567-e89b-12d3-a456-************",
  "model_used": "gemini-pro",
  "response_length": 150,
  "time": "2023-12-07T10:30:00Z"
}
```

### Health Checks

The `/health` endpoint provides detailed health information:

- Service status
- Active sessions count
- AI Studio connectivity (when detailed checks are enabled)
- Uptime and version information

## Troubleshooting

### Common Issues

#### 1. Authentication Failures

**Problem:** Getting 401 errors or authentication timeouts.

**Solutions:**
- Verify your Google account credentials are correct
- Check if 2FA is enabled (may require app-specific passwords)
- Ensure the browser profile has the correct permissions
- Try running in non-headless mode for debugging: `HEADLESS=false`

#### 2. Browser Launch Failures

**Problem:** Browser fails to start or crashes.

**Solutions:**
```bash
# Install browser dependencies (Linux)
sudo apt-get update
sudo apt-get install -y \
  libnss3 libatk-bridge2.0-0 libdrm2 libxkbcommon0 \
  libgtk-3-0 libgbm1 libasound2

# For Docker, use the playwright base image
# Check browser installation
go run github.com/playwright-community/playwright-go/cmd/playwright@latest install
```

#### 3. Rate Limiting

**Problem:** Getting 429 Too Many Requests errors.

**Solutions:**
- Reduce request frequency
- Implement client-side rate limiting
- Use multiple API keys for higher throughput
- Check AI Studio's usage policies

#### 4. Session Management Issues

**Problem:** Sessions expiring unexpectedly or not persisting.

**Solutions:**
- Check session TTL configuration
- Verify browser profile persistence
- Monitor session cleanup logs
- Ensure adequate disk space for profiles

#### 5. File Upload Problems

**Problem:** File uploads failing or timing out.

**Solutions:**
- Check file size limits (default: 10MB)
- Verify file type is supported
- Ensure proper base64 encoding
- Check network timeout settings

### Debug Mode

Enable debug logging for detailed troubleshooting:

```bash
LOG_LEVEL=DEBUG HEADLESS=false go run cmd/server/main.go
```

### Log Analysis

Key log fields to monitor:
- `request_id`: Track individual requests
- `session_id`: Monitor session lifecycle
- `error`: Identify failure patterns
- `latency`: Performance monitoring

## Performance Tuning

### Configuration Recommendations

**For High Throughput:**
```yaml
browser:
  max_contexts: 20
session:
  max_sessions: 200
  ttl_minutes: 30
rate_limit:
  requests_per_minute: 30
  burst_size: 50
```

**For Resource Constrained Environments:**
```yaml
browser:
  max_contexts: 5
session:
  max_sessions: 50
  ttl_minutes: 15
rate_limit:
  requests_per_minute: 5
  burst_size: 10
```

### Resource Requirements

**Minimum:**
- CPU: 1 core
- Memory: 512MB
- Disk: 1GB (for browser profiles)

**Recommended:**
- CPU: 2 cores
- Memory: 2GB
- Disk: 5GB (for profiles and logs)

**High Load:**
- CPU: 4+ cores
- Memory: 4GB+
- Disk: 10GB+

## Security Considerations

### API Key Management

- Use strong, unique API keys
- Rotate keys regularly
- Store keys securely (environment variables, secrets management)
- Monitor API key usage

### Network Security

- Use HTTPS in production
- Implement proper firewall rules
- Consider VPN or private networks
- Monitor for suspicious activity

### Browser Security

- Keep browser profiles isolated
- Regular profile cleanup
- Monitor for malicious content
- Use sandboxed environments

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit changes: `git commit -m 'Add amazing feature'`
4. Push to branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

### Development Guidelines

- Follow Go best practices
- Add tests for new features
- Update documentation
- Use conventional commit messages
- Ensure all tests pass

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

- 📖 [Documentation](docs/)
- 🐛 [Issue Tracker](https://github.com/your-org/aistudio-proxy/issues)
- 💬 [Discussions](https://github.com/your-org/aistudio-proxy/discussions)
- 📧 [Email Support](mailto:<EMAIL>)

## Changelog

See [CHANGELOG.md](CHANGELOG.md) for version history and updates.

## Acknowledgments

- [Playwright Go](https://github.com/playwright-community/playwright-go) for browser automation
- [Gin](https://github.com/gin-gonic/gin) for the web framework
- [Logrus](https://github.com/sirupsen/logrus) for structured logging
- Google AI Studio for the AI capabilities
