package aistudio

import (
	"context"
	"encoding/base64"
	"fmt"
	"strings"
	"time"

	"github.com/playwright-community/playwright-go"
	"github.com/sirupsen/logrus"

	"aistudio-proxy/internal/browser"
	"aistudio-proxy/internal/config"
)

// Client handles interactions with AI Studio
type Client struct {
	config  *config.Config
	logger  *logrus.Logger
	browser *browser.Manager
}

// ChatRequest represents a chat request
type ChatRequest struct {
	Message   string `json:"message" binding:"required" example:"Hello, how are you?"`
	Model     string `json:"model,omitempty" example:"gemini-pro"`
	SessionID string `json:"session_id,omitempty" example:"550e8400-e29b-41d4-a716-446655440000"`
	File      *File  `json:"file,omitempty"`
}

// File represents an uploaded file
type File struct {
	Name     string `json:"name" example:"document.pdf"`
	Content  string `json:"content" example:"base64-encoded-content"`
	MimeType string `json:"mime_type" example:"application/pdf"`
	Size     int64  `json:"size" example:"1024"`
}

// ChatResponse represents a chat response
type ChatResponse struct {
	Response  string    `json:"response" example:"Hello! I'm doing well, thank you for asking."`
	SessionID string    `json:"session_id" example:"550e8400-e29b-41d4-a716-446655440000"`
	ModelUsed string    `json:"model_used" example:"gemini-pro"`
	Timestamp time.Time `json:"timestamp" example:"2023-12-07T10:30:00Z"`
	Status    string    `json:"status" example:"success"`
	Error     *APIError `json:"error,omitempty"`
}

// APIError represents an API error
type APIError struct {
	Code    string      `json:"code" example:"RATE_LIMIT_EXCEEDED"`
	Message string      `json:"message" example:"Rate limit exceeded. Please try again later."`
	Details interface{} `json:"details,omitempty"`
}

// Model represents an AI Studio model
type Model struct {
	ID           string   `json:"id" example:"gemini-pro"`
	Name         string   `json:"name" example:"Gemini Pro"`
	Description  string   `json:"description" example:"Most capable model for complex reasoning tasks"`
	Capabilities []string `json:"capabilities" example:"text,images,documents"`
}

// NewClient creates a new AI Studio client
func NewClient(cfg *config.Config, logger *logrus.Logger, browserManager *browser.Manager) *Client {
	return &Client{
		config:  cfg,
		logger:  logger,
		browser: browserManager,
	}
}

// SendMessage sends a message to AI Studio
func (c *Client) SendMessage(ctx context.Context, req *ChatRequest) (*ChatResponse, error) {
	// Get browser context
	browserCtx, err := c.browser.GetContext(req.SessionID)
	if err != nil {
		return nil, fmt.Errorf("failed to get browser context: %w", err)
	}

	// Create a new page
	page, err := browserCtx.Context.NewPage()
	if err != nil {
		return nil, fmt.Errorf("failed to create new page: %w", err)
	}
	defer page.Close()

	// Navigate to AI Studio
	if err := c.navigateToAIStudio(ctx, page); err != nil {
		return nil, fmt.Errorf("failed to navigate to AI Studio: %w", err)
	}

	// Ensure authentication
	if err := c.ensureAuthenticated(ctx, page); err != nil {
		return nil, fmt.Errorf("authentication failed: %w", err)
	}

	// Select model if specified
	model := req.Model
	if model == "" {
		model = c.config.AIStudio.DefaultModel
	}

	if err := c.selectModel(ctx, page, model); err != nil {
		return nil, fmt.Errorf("failed to select model: %w", err)
	}

	// Upload file if provided
	if req.File != nil {
		if err := c.uploadFile(ctx, page, req.File); err != nil {
			return nil, fmt.Errorf("failed to upload file: %w", err)
		}
	}

	// Send message
	response, err := c.sendMessageToChat(ctx, page, req.Message)
	if err != nil {
		return nil, fmt.Errorf("failed to send message: %w", err)
	}

	return &ChatResponse{
		Response:  response,
		SessionID: req.SessionID,
		ModelUsed: model,
		Timestamp: time.Now(),
		Status:    "success",
	}, nil
}

// navigateToAIStudio navigates to the AI Studio website
func (c *Client) navigateToAIStudio(ctx context.Context, page playwright.Page) error {
	c.logger.Debug("Navigating to AI Studio")

	// Navigate with timeout
	_, err := page.Goto(c.config.AIStudio.BaseURL, playwright.PageGotoOptions{
		WaitUntil: playwright.WaitUntilStateNetworkidle,
		Timeout:   playwright.Float(30000), // 30 seconds
	})

	if err != nil {
		return fmt.Errorf("failed to navigate to AI Studio: %w", err)
	}

	// Wait for page to load
	if err := page.WaitForLoadState(playwright.PageWaitForLoadStateOptions{
		State: playwright.LoadStateNetworkidle,
	}); err != nil {
		return fmt.Errorf("page failed to load: %w", err)
	}

	return nil
}

// ensureAuthenticated ensures the user is authenticated
func (c *Client) ensureAuthenticated(ctx context.Context, page playwright.Page) error {
	c.logger.Debug("Checking authentication status")

	// Check if already authenticated by looking for chat interface
	chatInput := page.Locator("textarea[placeholder*='Enter a prompt'], textarea[aria-label*='Message'], .chat-input")
	if count, _ := chatInput.Count(); count > 0 {
		c.logger.Debug("Already authenticated")
		return nil
	}

	// Look for sign-in button
	signInButton := page.Locator("button:has-text('Sign in'), a:has-text('Sign in'), [data-testid='sign-in']")
	if count, _ := signInButton.Count(); count > 0 {
		c.logger.Debug("Clicking sign-in button")
		if err := signInButton.First().Click(); err != nil {
			return fmt.Errorf("failed to click sign-in button: %w", err)
		}

		// Wait for Google sign-in page
		if err := page.WaitForURL("**/accounts.google.com/**", playwright.PageWaitForURLOptions{
			Timeout: playwright.Float(10000),
		}); err != nil {
			return fmt.Errorf("failed to navigate to Google sign-in: %w", err)
		}
	}

	// Handle Google authentication
	return c.handleGoogleAuth(ctx, page)
}

// handleGoogleAuth handles Google authentication
func (c *Client) handleGoogleAuth(ctx context.Context, page playwright.Page) error {
	c.logger.Debug("Handling Google authentication")

	// Fill email
	emailInput := page.Locator("input[type='email'], input[id='identifierId']")
	if err := emailInput.WaitFor(playwright.LocatorWaitForOptions{
		Timeout: playwright.Float(10000),
	}); err != nil {
		return fmt.Errorf("email input not found: %w", err)
	}

	if err := emailInput.Fill(c.config.AIStudio.Email); err != nil {
		return fmt.Errorf("failed to fill email: %w", err)
	}

	// Click next
	nextButton := page.Locator("button:has-text('Next'), #identifierNext")
	if err := nextButton.Click(); err != nil {
		return fmt.Errorf("failed to click next button: %w", err)
	}

	// Wait for password input
	passwordInput := page.Locator("input[type='password'], input[name='password']")
	if err := passwordInput.WaitFor(playwright.LocatorWaitForOptions{
		Timeout: playwright.Float(10000),
	}); err != nil {
		return fmt.Errorf("password input not found: %w", err)
	}

	// Fill password
	if err := passwordInput.Fill(c.config.AIStudio.Password); err != nil {
		return fmt.Errorf("failed to fill password: %w", err)
	}

	// Click next/sign in
	signInButton := page.Locator("button:has-text('Next'), button:has-text('Sign in'), #passwordNext")
	if err := signInButton.Click(); err != nil {
		return fmt.Errorf("failed to click sign-in button: %w", err)
	}

	// Wait for redirect back to AI Studio
	if err := page.WaitForURL("**/aistudio.google.com/**", playwright.PageWaitForURLOptions{
		Timeout: playwright.Float(30000),
	}); err != nil {
		return fmt.Errorf("failed to redirect to AI Studio after authentication: %w", err)
	}

	// Wait for chat interface to appear
	chatInput := page.Locator("textarea[placeholder*='Enter a prompt'], textarea[aria-label*='Message'], .chat-input")
	if err := chatInput.WaitFor(playwright.LocatorWaitForOptions{
		Timeout: playwright.Float(15000),
	}); err != nil {
		return fmt.Errorf("chat interface not found after authentication: %w", err)
	}

	c.logger.Debug("Authentication successful")
	return nil
}

// selectModel selects the specified AI model
func (c *Client) selectModel(ctx context.Context, page playwright.Page, model string) error {
	c.logger.WithField("model", model).Debug("Selecting AI model")

	// Look for model selector
	modelSelector := page.Locator("[data-testid='model-selector'], .model-selector, button:has-text('Model')")
	if count, _ := modelSelector.Count(); count > 0 {
		if err := modelSelector.First().Click(); err != nil {
			return fmt.Errorf("failed to click model selector: %w", err)
		}

		// Wait for model options
		modelOption := page.Locator(fmt.Sprintf("button:has-text('%s'), [data-value='%s']", model, model))
		if err := modelOption.WaitFor(playwright.LocatorWaitForOptions{
			Timeout: playwright.Float(5000),
		}); err != nil {
			c.logger.WithField("model", model).Warn("Model selector not found, using default")
			return nil
		}

		if err := modelOption.Click(); err != nil {
			return fmt.Errorf("failed to select model: %w", err)
		}
	}

	return nil
}

// uploadFile uploads a file to the chat
func (c *Client) uploadFile(ctx context.Context, page playwright.Page, file *File) error {
	c.logger.WithField("filename", file.Name).Debug("Uploading file")

	// Decode base64 content
	content, err := base64.StdEncoding.DecodeString(file.Content)
	if err != nil {
		return fmt.Errorf("failed to decode file content: %w", err)
	}

	// Look for file upload button
	uploadButton := page.Locator("input[type='file'], button[aria-label*='upload'], .upload-button")
	if count, _ := uploadButton.Count(); count == 0 {
		return fmt.Errorf("file upload button not found")
	}

	// Set file content
	if err := uploadButton.First().SetInputFiles([]playwright.InputFile{
		{
			Name:     file.Name,
			MimeType: file.MimeType,
			Buffer:   content,
		},
	}); err != nil {
		return fmt.Errorf("failed to upload file: %w", err)
	}

	// Wait for file to be processed
	time.Sleep(2 * time.Second)

	return nil
}

// sendMessageToChat sends a message to the chat interface
func (c *Client) sendMessageToChat(ctx context.Context, page playwright.Page, message string) (string, error) {
	c.logger.Debug("Sending message to chat")

	// Find chat input
	chatInput := page.Locator("textarea[placeholder*='Enter a prompt'], textarea[aria-label*='Message'], .chat-input textarea")
	if err := chatInput.WaitFor(playwright.LocatorWaitForOptions{
		Timeout: playwright.Float(10000),
	}); err != nil {
		return "", fmt.Errorf("chat input not found: %w", err)
	}

	// Clear and fill message
	if err := chatInput.Clear(); err != nil {
		return "", fmt.Errorf("failed to clear chat input: %w", err)
	}

	if err := chatInput.Fill(message); err != nil {
		return "", fmt.Errorf("failed to fill message: %w", err)
	}

	// Find and click send button
	sendButton := page.Locator("button[aria-label*='Send'], button:has-text('Send'), .send-button")
	if err := sendButton.WaitFor(playwright.LocatorWaitForOptions{
		Timeout: playwright.Float(5000),
	}); err != nil {
		// Try pressing Enter if send button not found
		if err := chatInput.Press("Enter"); err != nil {
			return "", fmt.Errorf("failed to send message: %w", err)
		}
	} else {
		if err := sendButton.Click(); err != nil {
			return "", fmt.Errorf("failed to click send button: %w", err)
		}
	}

	// Wait for response
	return c.waitForResponse(ctx, page)
}

// waitForResponse waits for and extracts the AI response
func (c *Client) waitForResponse(ctx context.Context, page playwright.Page) (string, error) {
	c.logger.Debug("Waiting for AI response")

	// Wait for response to appear (look for loading indicators to disappear)
	loadingIndicator := page.Locator(".loading, .spinner, [data-testid='loading']")

	// Wait for loading to start (optional)
	time.Sleep(1 * time.Second)

	// Wait for loading to finish
	if count, _ := loadingIndicator.Count(); count > 0 {
		if err := loadingIndicator.WaitFor(playwright.LocatorWaitForOptions{
			State:   playwright.WaitForSelectorStateDetached,
			Timeout: playwright.Float(float64(c.config.AIStudio.RequestTimeout.Milliseconds())),
		}); err != nil {
			c.logger.WithError(err).Warn("Loading indicator timeout, proceeding anyway")
		}
	}

	// Additional wait for response to be complete
	time.Sleep(2 * time.Second)

	// Find the latest response
	responseSelectors := []string{
		".response-content:last-child",
		".message-content:last-child",
		"[data-testid='ai-response']:last-child",
		".chat-message:last-child .message-text",
		".response:last-child",
	}

	var response string
	var err error

	for _, selector := range responseSelectors {
		responseElement := page.Locator(selector)
		if count, _ := responseElement.Count(); count > 0 {
			response, err = responseElement.TextContent()
			if err == nil && strings.TrimSpace(response) != "" {
				break
			}
		}
	}

	if response == "" {
		// Fallback: try to get any recent message that's not our input
		allMessages := page.Locator(".chat-message, .message")
		if count, _ := allMessages.Count(); count > 0 {
			lastMessage := allMessages.Last()
			response, err = lastMessage.TextContent()
			if err != nil {
				return "", fmt.Errorf("failed to extract response: %w", err)
			}
		}
	}

	if strings.TrimSpace(response) == "" {
		return "", fmt.Errorf("no response received from AI Studio")
	}

	c.logger.WithField("response_length", len(response)).Debug("Received AI response")
	return strings.TrimSpace(response), nil
}

// GetAvailableModels returns the list of available AI models
func (c *Client) GetAvailableModels(ctx context.Context, sessionID string) ([]Model, error) {
	// Get browser context
	browserCtx, err := c.browser.GetContext(sessionID)
	if err != nil {
		return nil, fmt.Errorf("failed to get browser context: %w", err)
	}

	// Create a new page
	page, err := browserCtx.Context.NewPage()
	if err != nil {
		return nil, fmt.Errorf("failed to create new page: %w", err)
	}
	defer page.Close()

	// Navigate to AI Studio
	if err := c.navigateToAIStudio(ctx, page); err != nil {
		return nil, fmt.Errorf("failed to navigate to AI Studio: %w", err)
	}

	// Ensure authentication
	if err := c.ensureAuthenticated(ctx, page); err != nil {
		return nil, fmt.Errorf("authentication failed: %w", err)
	}

	// Default models (this would be enhanced to scrape actual available models)
	models := []Model{
		{
			ID:           "gemini-pro",
			Name:         "Gemini Pro",
			Description:  "Most capable model for complex reasoning tasks",
			Capabilities: []string{"text", "images", "documents"},
		},
		{
			ID:           "gemini-pro-vision",
			Name:         "Gemini Pro Vision",
			Description:  "Multimodal model that can understand text and images",
			Capabilities: []string{"text", "images", "vision"},
		},
		{
			ID:           "gemini-ultra",
			Name:         "Gemini Ultra",
			Description:  "Most capable model for highly complex tasks",
			Capabilities: []string{"text", "images", "documents", "code"},
		},
	}

	return models, nil
}

// ValidateConnection validates the connection to AI Studio
func (c *Client) ValidateConnection(ctx context.Context, sessionID string) error {
	// Get browser context
	browserCtx, err := c.browser.GetContext(sessionID)
	if err != nil {
		return fmt.Errorf("failed to get browser context: %w", err)
	}

	// Create a new page
	page, err := browserCtx.Context.NewPage()
	if err != nil {
		return fmt.Errorf("failed to create new page: %w", err)
	}
	defer page.Close()

	// Navigate to AI Studio
	if err := c.navigateToAIStudio(ctx, page); err != nil {
		return fmt.Errorf("failed to navigate to AI Studio: %w", err)
	}

	// Check if we can reach the authentication page
	if err := c.ensureAuthenticated(ctx, page); err != nil {
		return fmt.Errorf("authentication validation failed: %w", err)
	}

	return nil
}
