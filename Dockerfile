# Multi-stage build for AI Studio Proxy
FROM golang:1.21-alpine AS builder

# Install build dependencies
RUN apk add --no-cache git ca-certificates tzdata

# Set working directory
WORKDIR /app

# Copy go mod files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy source code
COPY . .

# Build the application
ARG VERSION=dev
ARG BUILD_TIME
ARG GIT_COMMIT
RUN CGO_ENABLED=0 GOOS=linux go build \
    -ldflags "-X main.Version=${VERSION} -X main.BuildTime=${BUILD_TIME} -X main.GitCommit=${GIT_COMMIT}" \
    -a -installsuffix cgo \
    -o aistudio-proxy \
    cmd/server/main.go

# Runtime stage
FROM mcr.microsoft.com/playwright:v1.40.0-focal

# Install additional dependencies
RUN apt-get update && apt-get install -y \
    ca-certificates \
    tzdata \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Set working directory
WORKDIR /app

# Copy binary from builder stage
COPY --from=builder /app/aistudio-proxy .

# Copy configuration template
COPY --from=builder /app/config.yaml.example ./config.yaml.example

# Create directories for browser profiles and logs
RUN mkdir -p /app/profiles /app/logs && \
    chown -R appuser:appuser /app

# Switch to non-root user
USER appuser

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# Set default environment variables
ENV PORT=8080
ENV LOG_LEVEL=INFO
ENV HEADLESS=true
ENV BROWSER_PROFILE_PATH=/app/profiles

# Run the application
CMD ["./aistudio-proxy"]
