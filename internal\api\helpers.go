package api

import (
	"encoding/base64"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"path/filepath"
	"strings"

	"github.com/gin-gonic/gin"

	"aistudio-proxy/internal/aistudio"
	"aistudio-proxy/internal/config"
)

// respondWithError sends a standardized error response
func (h *Handlers) respondWithError(c *gin.Context, statusCode int, errorCode, message string, details interface{}) {
	response := ErrorResponse{
		Error: APIError{
			Code:    errorCode,
			Message: message,
			Details: details,
		},
	}

	c<PERSON><PERSON>("X-Request-ID", GetRequestID(c))
	c.<PERSON>(statusCode, response)
}

// parseMultipartRequest parses multipart form data for file uploads
func (h *Handlers) parseMultipartRequest(c *gin.Context, req *aistudio.ChatRequest) error {
	// Parse multipart form
	if err := c.Request.ParseMultipartForm(h.config.Limits.MaxFileSize); err != nil {
		return fmt.Errorf("failed to parse multipart form: %w", err)
	}

	// Get message
	if messages := c.Request.MultipartForm.Value["message"]; len(messages) > 0 {
		req.Message = messages[0]
	}

	// Get model
	if models := c.Request.MultipartForm.Value["model"]; len(models) > 0 {
		req.Model = models[0]
	}

	// Get session ID
	if sessionIDs := c.Request.MultipartForm.Value["session_id"]; len(sessionIDs) > 0 {
		req.SessionID = sessionIDs[0]
	}

	// Handle file upload
	if files := c.Request.MultipartForm.File["file"]; len(files) > 0 {
		fileHeader := files[0]
		
		// Validate file size
		if fileHeader.Size > h.config.Limits.MaxFileSize {
			return fmt.Errorf("file size exceeds limit: %d bytes (max: %d bytes)", 
				fileHeader.Size, h.config.Limits.MaxFileSize)
		}

		// Validate file type
		ext := strings.ToLower(strings.TrimPrefix(filepath.Ext(fileHeader.Filename), "."))
		if !h.isAllowedFileType(ext) {
			return fmt.Errorf("file type not allowed: %s (allowed: %v)", 
				ext, h.config.Limits.AllowedFileTypes)
		}

		// Read file content
		file, err := fileHeader.Open()
		if err != nil {
			return fmt.Errorf("failed to open uploaded file: %w", err)
		}
		defer file.Close()

		content, err := io.ReadAll(file)
		if err != nil {
			return fmt.Errorf("failed to read file content: %w", err)
		}

		// Encode to base64
		encodedContent := base64.StdEncoding.EncodeToString(content)

		// Determine MIME type
		mimeType := h.getMimeType(ext)

		req.File = &aistudio.File{
			Name:     fileHeader.Filename,
			Content:  encodedContent,
			MimeType: mimeType,
			Size:     fileHeader.Size,
		}
	}

	return nil
}

// validateChatRequest validates a chat request
func (h *Handlers) validateChatRequest(req *aistudio.ChatRequest) error {
	// Validate message
	if strings.TrimSpace(req.Message) == "" {
		return fmt.Errorf("message is required")
	}

	if len(req.Message) > h.config.Limits.MaxMessageLength {
		return fmt.Errorf("message too long: %d characters (max: %d)", 
			len(req.Message), h.config.Limits.MaxMessageLength)
	}

	// Validate file if provided
	if req.File != nil {
		if req.File.Name == "" {
			return fmt.Errorf("file name is required")
		}

		if req.File.Content == "" {
			return fmt.Errorf("file content is required")
		}

		// Validate file size
		if req.File.Size > h.config.Limits.MaxFileSize {
			return fmt.Errorf("file size exceeds limit: %d bytes (max: %d bytes)", 
				req.File.Size, h.config.Limits.MaxFileSize)
		}

		// Validate file type
		ext := strings.ToLower(strings.TrimPrefix(filepath.Ext(req.File.Name), "."))
		if !h.isAllowedFileType(ext) {
			return fmt.Errorf("file type not allowed: %s (allowed: %v)", 
				ext, h.config.Limits.AllowedFileTypes)
		}

		// Validate base64 content
		if _, err := base64.StdEncoding.DecodeString(req.File.Content); err != nil {
			return fmt.Errorf("invalid base64 file content: %w", err)
		}
	}

	return nil
}

// isAllowedFileType checks if a file extension is allowed
func (h *Handlers) isAllowedFileType(ext string) bool {
	for _, allowedType := range h.config.Limits.AllowedFileTypes {
		if ext == allowedType {
			return true
		}
	}
	return false
}

// getMimeType returns the MIME type for a file extension
func (h *Handlers) getMimeType(ext string) string {
	mimeTypes := map[string]string{
		"jpg":  "image/jpeg",
		"jpeg": "image/jpeg",
		"png":  "image/png",
		"gif":  "image/gif",
		"pdf":  "application/pdf",
		"txt":  "text/plain",
		"docx": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
		"doc":  "application/msword",
	}

	if mimeType, exists := mimeTypes[ext]; exists {
		return mimeType
	}

	return "application/octet-stream"
}

// sanitizeInput sanitizes user input to prevent XSS and injection attacks
func (h *Handlers) sanitizeInput(input string) string {
	// Basic HTML escaping
	input = strings.ReplaceAll(input, "<", "&lt;")
	input = strings.ReplaceAll(input, ">", "&gt;")
	input = strings.ReplaceAll(input, "\"", "&quot;")
	input = strings.ReplaceAll(input, "'", "&#x27;")
	input = strings.ReplaceAll(input, "&", "&amp;")
	
	return input
}

// validateFileUpload validates an uploaded file
func (h *Handlers) validateFileUpload(fileHeader *multipart.FileHeader) error {
	// Check file size
	if fileHeader.Size > h.config.Limits.MaxFileSize {
		return fmt.Errorf("file size exceeds limit: %d bytes (max: %d bytes)", 
			fileHeader.Size, h.config.Limits.MaxFileSize)
	}

	// Check file extension
	ext := strings.ToLower(strings.TrimPrefix(filepath.Ext(fileHeader.Filename), "."))
	if !h.isAllowedFileType(ext) {
		return fmt.Errorf("file type not allowed: %s (allowed: %v)", 
			ext, h.config.Limits.AllowedFileTypes)
	}

	// Check filename for security
	if strings.Contains(fileHeader.Filename, "..") || 
	   strings.Contains(fileHeader.Filename, "/") || 
	   strings.Contains(fileHeader.Filename, "\\") {
		return fmt.Errorf("invalid filename: %s", fileHeader.Filename)
	}

	return nil
}

// processFileUpload processes an uploaded file and returns File struct
func (h *Handlers) processFileUpload(fileHeader *multipart.FileHeader) (*aistudio.File, error) {
	// Validate file
	if err := h.validateFileUpload(fileHeader); err != nil {
		return nil, err
	}

	// Open file
	file, err := fileHeader.Open()
	if err != nil {
		return nil, fmt.Errorf("failed to open uploaded file: %w", err)
	}
	defer file.Close()

	// Read content
	content, err := io.ReadAll(file)
	if err != nil {
		return nil, fmt.Errorf("failed to read file content: %w", err)
	}

	// Encode to base64
	encodedContent := base64.StdEncoding.EncodeToString(content)

	// Get file extension and MIME type
	ext := strings.ToLower(strings.TrimPrefix(filepath.Ext(fileHeader.Filename), "."))
	mimeType := h.getMimeType(ext)

	return &aistudio.File{
		Name:     fileHeader.Filename,
		Content:  encodedContent,
		MimeType: mimeType,
		Size:     fileHeader.Size,
	}, nil
}

// handlePanic recovers from panics and returns a proper error response
func (h *Handlers) handlePanic() gin.HandlerFunc {
	return gin.CustomRecovery(func(c *gin.Context, recovered interface{}) {
		h.logger.WithField("panic", recovered).Error("Handler panic recovered")
		
		h.respondWithError(c, http.StatusInternalServerError, "INTERNAL_ERROR", 
			"An internal error occurred", nil)
	})
}

// validateSessionID validates a session ID format
func (h *Handlers) validateSessionID(sessionID string) error {
	if sessionID == "" {
		return nil // Empty session ID is allowed for auto-creation
	}

	// Basic UUID format validation
	if len(sessionID) != 36 {
		return fmt.Errorf("invalid session ID format")
	}

	// Check for valid UUID pattern
	parts := strings.Split(sessionID, "-")
	if len(parts) != 5 {
		return fmt.Errorf("invalid session ID format")
	}

	return nil
}
