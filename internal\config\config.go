package config

import (
	"fmt"
	"os"
	"strconv"
	"strings"
	"time"

	"gopkg.in/yaml.v3"
)

// Config represents the application configuration
type Config struct {
	Server      ServerConfig      `yaml:"server"`
	Auth        AuthConfig        `yaml:"auth"`
	AIStudio    AIStudioConfig    `yaml:"aistudio"`
	Browser     BrowserConfig     `yaml:"browser"`
	Session     SessionConfig     `yaml:"session"`
	RateLimit   RateLimitConfig   `yaml:"rate_limit"`
	Limits      LimitsConfig      `yaml:"limits"`
	CORS        CORSConfig        `yaml:"cors"`
	Logging     LoggingConfig     `yaml:"logging"`
	Metrics     MetricsConfig     `yaml:"metrics"`
	Health      HealthConfig      `yaml:"health"`
	Retry       RetryConfig       `yaml:"retry"`
	Development DevelopmentConfig `yaml:"development"`
}

// ServerConfig holds server-related configuration
type ServerConfig struct {
	Port            int           `yaml:"port"`
	Host            string        `yaml:"host"`
	ReadTimeout     time.Duration `yaml:"read_timeout"`
	WriteTimeout    time.Duration `yaml:"write_timeout"`
	IdleTimeout     time.Duration `yaml:"idle_timeout"`
	ShutdownTimeout time.Duration `yaml:"shutdown_timeout"`
}

// AuthConfig holds authentication configuration
type AuthConfig struct {
	APIKeys []string `yaml:"api_keys"`
	Enabled bool     `yaml:"enabled"`
}

// AIStudioConfig holds AI Studio specific configuration
type AIStudioConfig struct {
	BaseURL        string        `yaml:"base_url"`
	Email          string        `yaml:"email"`
	Password       string        `yaml:"password"`
	DefaultModel   string        `yaml:"default_model"`
	RequestTimeout time.Duration `yaml:"request_timeout"`
	UploadTimeout  time.Duration `yaml:"upload_timeout"`
}

// BrowserConfig holds browser-related configuration
type BrowserConfig struct {
	Type           string        `yaml:"type"`
	ProfilePath    string        `yaml:"profile_path"`
	Headless       bool          `yaml:"headless"`
	LaunchTimeout  time.Duration `yaml:"launch_timeout"`
	MaxContexts    int           `yaml:"max_contexts"`
	ContextTimeout time.Duration `yaml:"context_timeout"`
	Args           []string      `yaml:"args"`
}

// SessionConfig holds session management configuration
type SessionConfig struct {
	TTLMinutes         int           `yaml:"ttl_minutes"`
	MaxSessions        int           `yaml:"max_sessions"`
	CleanupInterval    time.Duration `yaml:"cleanup_interval"`
	PersistenceEnabled bool          `yaml:"persistence_enabled"`
}

// RateLimitConfig holds rate limiting configuration
type RateLimitConfig struct {
	Enabled           bool          `yaml:"enabled"`
	RequestsPerMinute int           `yaml:"requests_per_minute"`
	BurstSize         int           `yaml:"burst_size"`
	CleanupInterval   time.Duration `yaml:"cleanup_interval"`
}

// LimitsConfig holds request limits configuration
type LimitsConfig struct {
	MaxRequestSize   int64    `yaml:"max_request_size"`
	MaxFileSize      int64    `yaml:"max_file_size"`
	MaxMessageLength int      `yaml:"max_message_length"`
	AllowedFileTypes []string `yaml:"allowed_file_types"`
}

// CORSConfig holds CORS configuration
type CORSConfig struct {
	Enabled          bool     `yaml:"enabled"`
	AllowedOrigins   []string `yaml:"allowed_origins"`
	AllowedMethods   []string `yaml:"allowed_methods"`
	AllowedHeaders   []string `yaml:"allowed_headers"`
	ExposedHeaders   []string `yaml:"exposed_headers"`
	AllowCredentials bool     `yaml:"allow_credentials"`
	MaxAge           int      `yaml:"max_age"`
}

// LoggingConfig holds logging configuration
type LoggingConfig struct {
	Level           string `yaml:"level"`
	Format          string `yaml:"format"`
	Output          string `yaml:"output"`
	FilePath        string `yaml:"file_path"`
	RequestLogging  bool   `yaml:"request_logging"`
	ResponseLogging bool   `yaml:"response_logging"`
}

// MetricsConfig holds metrics configuration
type MetricsConfig struct {
	Enabled  bool   `yaml:"enabled"`
	Path     string `yaml:"path"`
	Detailed bool   `yaml:"detailed"`
}

// HealthConfig holds health check configuration
type HealthConfig struct {
	Path     string        `yaml:"path"`
	Detailed bool          `yaml:"detailed"`
	Timeout  time.Duration `yaml:"timeout"`
}

// RetryConfig holds retry logic configuration
type RetryConfig struct {
	Enabled           bool          `yaml:"enabled"`
	MaxRetries        int           `yaml:"max_retries"`
	InitialDelay      time.Duration `yaml:"initial_delay"`
	MaxDelay          time.Duration `yaml:"max_delay"`
	BackoffMultiplier float64       `yaml:"backoff_multiplier"`
	Jitter            float64       `yaml:"jitter"`
}

// DevelopmentConfig holds development-specific configuration
type DevelopmentConfig struct {
	Enabled        bool `yaml:"enabled"`
	DebugEndpoints bool `yaml:"debug_endpoints"`
	DumpRequests   bool `yaml:"dump_requests"`
	MockResponses  bool `yaml:"mock_responses"`
}

// Load loads configuration from environment variables and config file
func Load() (*Config, error) {
	// Start with default configuration
	cfg := getDefaultConfig()

	// Load from config file if it exists
	if err := loadFromFile(cfg, "config.yaml"); err != nil {
		// If config.yaml doesn't exist, try loading from environment
		if !os.IsNotExist(err) {
			return nil, fmt.Errorf("failed to load config file: %w", err)
		}
	}

	// Override with environment variables
	loadFromEnv(cfg)

	// Validate configuration
	if err := validate(cfg); err != nil {
		return nil, fmt.Errorf("configuration validation failed: %w", err)
	}

	return cfg, nil
}

// getDefaultConfig returns the default configuration
func getDefaultConfig() *Config {
	return &Config{
		Server: ServerConfig{
			Port:            8080,
			Host:            "0.0.0.0",
			ReadTimeout:     30 * time.Second,
			WriteTimeout:    30 * time.Second,
			IdleTimeout:     60 * time.Second,
			ShutdownTimeout: 30 * time.Second,
		},
		Auth: AuthConfig{
			APIKeys: []string{},
			Enabled: true,
		},
		AIStudio: AIStudioConfig{
			BaseURL:        "https://aistudio.google.com",
			DefaultModel:   "gemini-pro",
			RequestTimeout: 30 * time.Second,
			UploadTimeout:  60 * time.Second,
		},
		Browser: BrowserConfig{
			Type:           "chrome",
			Headless:       true,
			LaunchTimeout:  30 * time.Second,
			MaxContexts:    10,
			ContextTimeout: 3600 * time.Second,
			Args:           []string{"--no-sandbox", "--disable-dev-shm-usage", "--disable-gpu"},
		},
		Session: SessionConfig{
			TTLMinutes:         60,
			MaxSessions:        100,
			CleanupInterval:    5 * time.Minute,
			PersistenceEnabled: false,
		},
		RateLimit: RateLimitConfig{
			Enabled:           true,
			RequestsPerMinute: 10,
			BurstSize:         20,
			CleanupInterval:   1 * time.Minute,
		},
		Limits: LimitsConfig{
			MaxRequestSize:   1048576,  // 1MB
			MaxFileSize:      10485760, // 10MB
			MaxMessageLength: 10000,
			AllowedFileTypes: []string{"jpg", "jpeg", "png", "gif", "pdf", "txt", "docx", "doc"},
		},
		CORS: CORSConfig{
			Enabled:          true,
			AllowedOrigins:   []string{"*"},
			AllowedMethods:   []string{"GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"},
			AllowedHeaders:   []string{"Origin", "Content-Type", "Accept", "Authorization", "X-Request-ID"},
			ExposedHeaders:   []string{"X-Request-ID"},
			AllowCredentials: true,
			MaxAge:           86400,
		},
		Logging: LoggingConfig{
			Level:           "INFO",
			Format:          "json",
			Output:          "stdout",
			RequestLogging:  true,
			ResponseLogging: false,
		},
		Metrics: MetricsConfig{
			Enabled:  true,
			Path:     "/metrics",
			Detailed: false,
		},
		Health: HealthConfig{
			Path:     "/health",
			Detailed: true,
			Timeout:  5 * time.Second,
		},
		Retry: RetryConfig{
			Enabled:           true,
			MaxRetries:        3,
			InitialDelay:      1 * time.Second,
			MaxDelay:          30 * time.Second,
			BackoffMultiplier: 2.0,
			Jitter:            0.1,
		},
		Development: DevelopmentConfig{
			Enabled:        false,
			DebugEndpoints: false,
			DumpRequests:   false,
			MockResponses:  false,
		},
	}
}

// loadFromFile loads configuration from a YAML file
func loadFromFile(cfg *Config, filename string) error {
	data, err := os.ReadFile(filename)
	if err != nil {
		return err
	}

	return yaml.Unmarshal(data, cfg)
}

// loadFromEnv loads configuration from environment variables
func loadFromEnv(cfg *Config) {
	// Server configuration
	if port := os.Getenv("PORT"); port != "" {
		if p, err := strconv.Atoi(port); err == nil {
			cfg.Server.Port = p
		}
	}
	if host := os.Getenv("HOST"); host != "" {
		cfg.Server.Host = host
	}

	// Authentication configuration
	if apiKeys := os.Getenv("API_KEYS"); apiKeys != "" {
		cfg.Auth.APIKeys = strings.Split(apiKeys, ",")
		for i, key := range cfg.Auth.APIKeys {
			cfg.Auth.APIKeys[i] = strings.TrimSpace(key)
		}
	}
	if enabled := os.Getenv("AUTH_ENABLED"); enabled != "" {
		cfg.Auth.Enabled = enabled == "true"
	}

	// AI Studio configuration
	if email := os.Getenv("AI_STUDIO_EMAIL"); email != "" {
		cfg.AIStudio.Email = email
	}
	if password := os.Getenv("AI_STUDIO_PASSWORD"); password != "" {
		cfg.AIStudio.Password = password
	}
	if model := os.Getenv("AI_STUDIO_DEFAULT_MODEL"); model != "" {
		cfg.AIStudio.DefaultModel = model
	}

	// Browser configuration
	if profilePath := os.Getenv("BROWSER_PROFILE_PATH"); profilePath != "" {
		cfg.Browser.ProfilePath = profilePath
	}
	if headless := os.Getenv("HEADLESS"); headless != "" {
		cfg.Browser.Headless = headless == "true"
	}
	if browserType := os.Getenv("BROWSER_TYPE"); browserType != "" {
		cfg.Browser.Type = browserType
	}

	// Session configuration
	if ttl := os.Getenv("SESSION_TTL_MINUTES"); ttl != "" {
		if t, err := strconv.Atoi(ttl); err == nil {
			cfg.Session.TTLMinutes = t
		}
	}

	// Logging configuration
	if level := os.Getenv("LOG_LEVEL"); level != "" {
		cfg.Logging.Level = level
	}
	if format := os.Getenv("LOG_FORMAT"); format != "" {
		cfg.Logging.Format = format
	}

	// Development configuration
	if dev := os.Getenv("DEVELOPMENT_MODE"); dev != "" {
		cfg.Development.Enabled = dev == "true"
	}
	if mock := os.Getenv("MOCK_RESPONSES"); mock != "" {
		cfg.Development.MockResponses = mock == "true"
	}
}

// validate validates the configuration
func validate(cfg *Config) error {
	// Validate required fields
	if cfg.Auth.Enabled && len(cfg.Auth.APIKeys) == 0 {
		return fmt.Errorf("API keys are required when authentication is enabled")
	}

	if cfg.AIStudio.Email == "" {
		return fmt.Errorf("AI Studio email is required")
	}

	if cfg.AIStudio.Password == "" {
		return fmt.Errorf("AI Studio password is required")
	}

	// Validate ranges
	if cfg.Server.Port < 1 || cfg.Server.Port > 65535 {
		return fmt.Errorf("server port must be between 1 and 65535")
	}

	if cfg.Browser.MaxContexts < 1 {
		return fmt.Errorf("max browser contexts must be at least 1")
	}

	if cfg.Session.MaxSessions < 1 {
		return fmt.Errorf("max sessions must be at least 1")
	}

	if cfg.RateLimit.RequestsPerMinute < 1 {
		return fmt.Errorf("requests per minute must be at least 1")
	}

	// Validate browser type
	validBrowserTypes := []string{"chrome", "chromium", "opera", "firefox"}
	validType := false
	for _, t := range validBrowserTypes {
		if cfg.Browser.Type == t {
			validType = true
			break
		}
	}
	if !validType {
		return fmt.Errorf("invalid browser type: %s (valid types: %s)",
			cfg.Browser.Type, strings.Join(validBrowserTypes, ", "))
	}

	return nil
}
