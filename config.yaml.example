# AI Studio Proxy Configuration
# Copy this file to config.yaml and customize as needed

# Server Configuration
server:
  port: 8080
  host: "0.0.0.0"
  read_timeout: 30s
  write_timeout: 30s
  idle_timeout: 60s
  shutdown_timeout: 30s

# Authentication Configuration
auth:
  # Comma-separated list of API keys for Bearer token authentication
  api_keys: "your-api-key-1,your-api-key-2"
  # Enable API key authentication (set to false for development only)
  enabled: true

# AI Studio Configuration
aistudio:
  # Base URL for AI Studio
  base_url: "https://aistudio.google.com"
  # Login credentials (required for automated authentication)
  email: "<EMAIL>"
  password: "your-password"
  # Default model to use if not specified in requests
  default_model: "gemini-pro"
  # Request timeout for AI Studio interactions
  request_timeout: 30s
  # File upload timeout
  upload_timeout: 60s

# Browser Configuration
browser:
  # Browser type: chrome, chromium, opera
  type: "chrome"
  # Path to existing browser profile (optional)
  profile_path: ""
  # Run browser in headless mode
  headless: true
  # Browser launch timeout
  launch_timeout: 30s
  # Maximum number of concurrent browser contexts
  max_contexts: 10
  # Context idle timeout before cleanup
  context_timeout: 3600s
  # Browser arguments (optional)
  args:
    - "--no-sandbox"
    - "--disable-dev-shm-usage"
    - "--disable-gpu"

# Session Management
session:
  # Default session TTL in minutes
  ttl_minutes: 60
  # Maximum number of concurrent sessions
  max_sessions: 100
  # Session cleanup interval
  cleanup_interval: 5m
  # Enable session persistence across server restarts
  persistence_enabled: false

# Rate Limiting
rate_limit:
  # Enable rate limiting
  enabled: true
  # Requests per minute per session
  requests_per_minute: 10
  # Burst size
  burst_size: 20
  # Rate limit cleanup interval
  cleanup_interval: 1m

# Request Limits
limits:
  # Maximum request body size (in bytes)
  max_request_size: 1048576  # 1MB
  # Maximum file upload size (in bytes)
  max_file_size: 10485760    # 10MB
  # Maximum message length (characters)
  max_message_length: 10000
  # Supported file types for uploads
  allowed_file_types:
    - "jpg"
    - "jpeg"
    - "png"
    - "gif"
    - "pdf"
    - "txt"
    - "docx"
    - "doc"

# CORS Configuration
cors:
  # Enable CORS
  enabled: true
  # Allowed origins (* for all)
  allowed_origins:
    - "*"
  # Allowed methods
  allowed_methods:
    - "GET"
    - "POST"
    - "PUT"
    - "DELETE"
    - "PATCH"
    - "OPTIONS"
  # Allowed headers
  allowed_headers:
    - "Origin"
    - "Content-Type"
    - "Accept"
    - "Authorization"
    - "X-Request-ID"
  # Exposed headers
  exposed_headers:
    - "X-Request-ID"
  # Allow credentials
  allow_credentials: true
  # Max age for preflight requests
  max_age: 86400

# Logging Configuration
logging:
  # Log level: DEBUG, INFO, WARN, ERROR
  level: "INFO"
  # Log format: json, text
  format: "json"
  # Log output: stdout, stderr, file
  output: "stdout"
  # Log file path (if output is file)
  file_path: "logs/aistudio-proxy.log"
  # Enable request logging
  request_logging: true
  # Enable response logging (be careful with sensitive data)
  response_logging: false

# Metrics Configuration
metrics:
  # Enable Prometheus metrics
  enabled: true
  # Metrics endpoint path
  path: "/metrics"
  # Enable detailed metrics (may impact performance)
  detailed: false

# Health Check Configuration
health:
  # Health check endpoint path
  path: "/health"
  # Enable detailed health checks
  detailed: true
  # Health check timeout
  timeout: 5s

# Retry Configuration
retry:
  # Enable retry logic
  enabled: true
  # Maximum number of retries
  max_retries: 3
  # Initial retry delay
  initial_delay: 1s
  # Maximum retry delay
  max_delay: 30s
  # Backoff multiplier
  backoff_multiplier: 2.0
  # Jitter factor (0.0 to 1.0)
  jitter: 0.1

# Development Configuration
development:
  # Enable development mode
  enabled: false
  # Enable debug endpoints
  debug_endpoints: false
  # Enable request/response dumping
  dump_requests: false
  # Mock AI Studio responses (for testing)
  mock_responses: false
