package session

import (
	"fmt"
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"

	"aistudio-proxy/internal/config"
)

// Manager manages user sessions
type Manager struct {
	config   *config.Config
	logger   *logrus.Logger
	sessions map[string]*Session
	mu       sync.RWMutex
	closed   bool
	closeMu  sync.RWMutex
}

// Session represents a user session
type Session struct {
	ID        string            `json:"id"`
	Model     string            `json:"model"`
	CreatedAt time.Time         `json:"created_at"`
	ExpiresAt time.Time         `json:"expires_at"`
	LastUsed  time.Time         `json:"last_used"`
	Metadata  map[string]string `json:"metadata"`
	mu        sync.RWMutex
}

// CreateSessionRequest represents a session creation request
type CreateSessionRequest struct {
	Model      string `json:"model,omitempty" example:"gemini-pro"`
	TTLMinutes int    `json:"ttl_minutes,omitempty" example:"60"`
}

// CreateSessionResponse represents a session creation response
type CreateSessionResponse struct {
	SessionID string    `json:"session_id" example:"550e8400-e29b-41d4-a716-446655440000"`
	ExpiresAt time.Time `json:"expires_at" example:"2023-12-07T11:30:00Z"`
	Model     string    `json:"model" example:"gemini-pro"`
}

// SessionInfo represents session information
type SessionInfo struct {
	ID        string            `json:"id" example:"550e8400-e29b-41d4-a716-446655440000"`
	Model     string            `json:"model" example:"gemini-pro"`
	CreatedAt time.Time         `json:"created_at" example:"2023-12-07T10:30:00Z"`
	ExpiresAt time.Time         `json:"expires_at" example:"2023-12-07T11:30:00Z"`
	LastUsed  time.Time         `json:"last_used" example:"2023-12-07T10:45:00Z"`
	Metadata  map[string]string `json:"metadata,omitempty"`
}

// NewManager creates a new session manager
func NewManager(cfg *config.Config, logger *logrus.Logger) *Manager {
	manager := &Manager{
		config:   cfg,
		logger:   logger,
		sessions: make(map[string]*Session),
	}

	// Start cleanup routine
	go manager.cleanupRoutine()

	logger.WithFields(logrus.Fields{
		"max_sessions":     cfg.Session.MaxSessions,
		"ttl_minutes":      cfg.Session.TTLMinutes,
		"cleanup_interval": cfg.Session.CleanupInterval,
	}).Info("Session manager initialized")

	return manager
}

// CreateSession creates a new session
func (m *Manager) CreateSession(req *CreateSessionRequest) (*CreateSessionResponse, error) {
	m.closeMu.RLock()
	if m.closed {
		m.closeMu.RUnlock()
		return nil, fmt.Errorf("session manager is closed")
	}
	m.closeMu.RUnlock()

	m.mu.Lock()
	defer m.mu.Unlock()

	// Check if we've reached the maximum number of sessions
	if len(m.sessions) >= m.config.Session.MaxSessions {
		return nil, fmt.Errorf("maximum number of sessions reached (%d)", m.config.Session.MaxSessions)
	}

	// Generate session ID
	sessionID := uuid.New().String()

	// Determine TTL
	ttlMinutes := req.TTLMinutes
	if ttlMinutes <= 0 {
		ttlMinutes = m.config.Session.TTLMinutes
	}

	// Determine model
	model := req.Model
	if model == "" {
		model = m.config.AIStudio.DefaultModel
	}

	// Create session
	now := time.Now()
	session := &Session{
		ID:        sessionID,
		Model:     model,
		CreatedAt: now,
		ExpiresAt: now.Add(time.Duration(ttlMinutes) * time.Minute),
		LastUsed:  now,
		Metadata:  make(map[string]string),
	}

	m.sessions[sessionID] = session

	m.logger.WithFields(logrus.Fields{
		"session_id":     sessionID,
		"model":          model,
		"ttl_minutes":    ttlMinutes,
		"total_sessions": len(m.sessions),
	}).Info("Created new session")

	return &CreateSessionResponse{
		SessionID: sessionID,
		ExpiresAt: session.ExpiresAt,
		Model:     model,
	}, nil
}

// GetSession retrieves a session by ID
func (m *Manager) GetSession(sessionID string) (*Session, error) {
	m.closeMu.RLock()
	if m.closed {
		m.closeMu.RUnlock()
		return nil, fmt.Errorf("session manager is closed")
	}
	m.closeMu.RUnlock()

	m.mu.RLock()
	defer m.mu.RUnlock()

	session, exists := m.sessions[sessionID]
	if !exists {
		return nil, fmt.Errorf("session not found: %s", sessionID)
	}

	// Check if session is expired
	if time.Now().After(session.ExpiresAt) {
		return nil, fmt.Errorf("session expired: %s", sessionID)
	}

	// Update last used time
	session.mu.Lock()
	session.LastUsed = time.Now()
	session.mu.Unlock()

	return session, nil
}

// UpdateSession updates session metadata
func (m *Manager) UpdateSession(sessionID string, metadata map[string]string) error {
	m.closeMu.RLock()
	if m.closed {
		m.closeMu.RUnlock()
		return fmt.Errorf("session manager is closed")
	}
	m.closeMu.RUnlock()

	m.mu.RLock()
	session, exists := m.sessions[sessionID]
	m.mu.RUnlock()

	if !exists {
		return fmt.Errorf("session not found: %s", sessionID)
	}

	// Check if session is expired
	if time.Now().After(session.ExpiresAt) {
		return fmt.Errorf("session expired: %s", sessionID)
	}

	session.mu.Lock()
	defer session.mu.Unlock()

	// Update metadata
	for key, value := range metadata {
		session.Metadata[key] = value
	}
	session.LastUsed = time.Now()

	return nil
}

// RenewSession extends the session TTL
func (m *Manager) RenewSession(sessionID string, ttlMinutes int) error {
	m.closeMu.RLock()
	if m.closed {
		m.closeMu.RUnlock()
		return fmt.Errorf("session manager is closed")
	}
	m.closeMu.RUnlock()

	m.mu.RLock()
	session, exists := m.sessions[sessionID]
	m.mu.RUnlock()

	if !exists {
		return fmt.Errorf("session not found: %s", sessionID)
	}

	if ttlMinutes <= 0 {
		ttlMinutes = m.config.Session.TTLMinutes
	}

	session.mu.Lock()
	defer session.mu.Unlock()

	session.ExpiresAt = time.Now().Add(time.Duration(ttlMinutes) * time.Minute)
	session.LastUsed = time.Now()

	m.logger.WithFields(logrus.Fields{
		"session_id":  sessionID,
		"ttl_minutes": ttlMinutes,
		"expires_at":  session.ExpiresAt,
	}).Debug("Renewed session")

	return nil
}

// DeleteSession removes a session
func (m *Manager) DeleteSession(sessionID string) error {
	m.closeMu.RLock()
	if m.closed {
		m.closeMu.RUnlock()
		return fmt.Errorf("session manager is closed")
	}
	m.closeMu.RUnlock()

	m.mu.Lock()
	defer m.mu.Unlock()

	if _, exists := m.sessions[sessionID]; !exists {
		return fmt.Errorf("session not found: %s", sessionID)
	}

	delete(m.sessions, sessionID)

	m.logger.WithFields(logrus.Fields{
		"session_id":     sessionID,
		"total_sessions": len(m.sessions),
	}).Info("Deleted session")

	return nil
}

// ListSessions returns information about all active sessions
func (m *Manager) ListSessions() ([]SessionInfo, error) {
	m.closeMu.RLock()
	if m.closed {
		m.closeMu.RUnlock()
		return nil, fmt.Errorf("session manager is closed")
	}
	m.closeMu.RUnlock()

	m.mu.RLock()
	defer m.mu.RUnlock()

	sessions := make([]SessionInfo, 0, len(m.sessions))
	now := time.Now()

	for _, session := range m.sessions {
		// Skip expired sessions
		if now.After(session.ExpiresAt) {
			continue
		}

		session.mu.RLock()
		sessionInfo := SessionInfo{
			ID:        session.ID,
			Model:     session.Model,
			CreatedAt: session.CreatedAt,
			ExpiresAt: session.ExpiresAt,
			LastUsed:  session.LastUsed,
			Metadata:  make(map[string]string),
		}

		// Copy metadata
		for k, v := range session.Metadata {
			sessionInfo.Metadata[k] = v
		}
		session.mu.RUnlock()

		sessions = append(sessions, sessionInfo)
	}

	return sessions, nil
}

// GetSessionCount returns the current number of active sessions
func (m *Manager) GetSessionCount() int {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return len(m.sessions)
}

// cleanupRoutine periodically cleans up expired sessions
func (m *Manager) cleanupRoutine() {
	ticker := time.NewTicker(m.config.Session.CleanupInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			m.cleanupExpiredSessions()
		}
	}
}

// cleanupExpiredSessions removes expired sessions
func (m *Manager) cleanupExpiredSessions() {
	m.mu.Lock()
	defer m.mu.Unlock()

	now := time.Now()
	expiredSessions := make([]string, 0)

	for sessionID, session := range m.sessions {
		if now.After(session.ExpiresAt) {
			expiredSessions = append(expiredSessions, sessionID)
		}
	}

	for _, sessionID := range expiredSessions {
		delete(m.sessions, sessionID)
	}

	if len(expiredSessions) > 0 {
		m.logger.WithFields(logrus.Fields{
			"cleaned_sessions": len(expiredSessions),
			"total_sessions":   len(m.sessions),
		}).Info("Cleaned up expired sessions")
	}
}

// Close closes the session manager
func (m *Manager) Close() error {
	m.closeMu.Lock()
	defer m.closeMu.Unlock()

	if m.closed {
		return nil
	}

	m.closed = true

	// Clear all sessions
	m.mu.Lock()
	sessionCount := len(m.sessions)
	m.sessions = make(map[string]*Session)
	m.mu.Unlock()

	m.logger.WithField("cleared_sessions", sessionCount).Info("Session manager closed")
	return nil
}

// ValidateSessionID checks if a session ID is valid and not expired
func (m *Manager) ValidateSessionID(sessionID string) error {
	if sessionID == "" {
		return fmt.Errorf("session ID is required")
	}

	// Check if it's a valid UUID format
	if _, err := uuid.Parse(sessionID); err != nil {
		return fmt.Errorf("invalid session ID format: %w", err)
	}

	// Check if session exists and is not expired
	_, err := m.GetSession(sessionID)
	return err
}

// GetOrCreateSession gets an existing session or creates a new one
func (m *Manager) GetOrCreateSession(sessionID string, req *CreateSessionRequest) (*Session, bool, error) {
	// If no session ID provided, create a new one
	if sessionID == "" {
		createResp, err := m.CreateSession(req)
		if err != nil {
			return nil, false, err
		}

		session, err := m.GetSession(createResp.SessionID)
		return session, true, err
	}

	// Try to get existing session
	session, err := m.GetSession(sessionID)
	if err != nil {
		// If session doesn't exist or is expired, create a new one
		createResp, createErr := m.CreateSession(req)
		if createErr != nil {
			return nil, false, createErr
		}

		newSession, getErr := m.GetSession(createResp.SessionID)
		return newSession, true, getErr
	}

	return session, false, nil
}
