.PHONY: build test run docker-build generate-docs clean install-deps setup

# Variables
BINARY_NAME=aistudio-proxy
DOCKER_IMAGE=aistudio-proxy:latest
VERSION?=v1.0.0
BUILD_TIME=$(shell date -u +%Y-%m-%dT%H:%M:%SZ)
GIT_COMMIT=$(shell git rev-parse --short HEAD 2>/dev/null || echo "unknown")

# Build flags
LDFLAGS=-ldflags "-X main.Version=$(VERSION) -X main.BuildTime=$(BUILD_TIME) -X main.GitCommit=$(GIT_COMMIT)"

# Default target
all: build

# Setup project dependencies and directories
setup:
	@echo "Setting up project structure..."
	@mkdir -p cmd/server internal/api internal/browser internal/session internal/config internal/aistudio pkg docs docker
	@echo "Installing Go dependencies..."
	go mod tidy
	@echo "Installing Playwright browsers..."
	go run github.com/playwright-community/playwright-go/cmd/playwright@latest install
	@echo "Setup complete!"

# Install dependencies
install-deps:
	go mod download
	go mod tidy

# Build the application
build:
	@echo "Building $(BINARY_NAME)..."
	go build $(LDFLAGS) -o bin/$(BINARY_NAME) cmd/server/main.go

# Build for multiple platforms
build-all:
	@echo "Building for multiple platforms..."
	GOOS=linux GOARCH=amd64 go build $(LDFLAGS) -o bin/$(BINARY_NAME)-linux-amd64 cmd/server/main.go
	GOOS=windows GOARCH=amd64 go build $(LDFLAGS) -o bin/$(BINARY_NAME)-windows-amd64.exe cmd/server/main.go
	GOOS=darwin GOARCH=amd64 go build $(LDFLAGS) -o bin/$(BINARY_NAME)-darwin-amd64 cmd/server/main.go

# Run the application
run:
	@echo "Running $(BINARY_NAME)..."
	go run cmd/server/main.go

# Run with development settings
dev:
	@echo "Running in development mode..."
	HEADLESS=false LOG_LEVEL=DEBUG go run cmd/server/main.go

# Run tests
test:
	@echo "Running tests..."
	go test -v -race -coverprofile=coverage.out ./...

# Run tests with coverage report
test-coverage:
	@echo "Running tests with coverage..."
	go test -v -race -coverprofile=coverage.out ./...
	go tool cover -html=coverage.out -o coverage.html
	@echo "Coverage report generated: coverage.html"

# Run integration tests
test-integration:
	@echo "Running integration tests..."
	go test -v -tags=integration ./...

# Run load tests
test-load:
	@echo "Running load tests..."
	go test -v -tags=load ./...

# Generate API documentation
generate-docs:
	@echo "Generating API documentation..."
	swag init -g cmd/server/main.go -o docs/
	@echo "Documentation generated in docs/"

# Format code
fmt:
	@echo "Formatting code..."
	go fmt ./...

# Lint code
lint:
	@echo "Linting code..."
	golangci-lint run

# Security scan
security:
	@echo "Running security scan..."
	gosec ./...

# Build Docker image
docker-build:
	@echo "Building Docker image..."
	docker build -t $(DOCKER_IMAGE) .

# Run with Docker Compose
docker-up:
	@echo "Starting services with Docker Compose..."
	docker-compose up -d

# Stop Docker Compose services
docker-down:
	@echo "Stopping Docker Compose services..."
	docker-compose down

# Clean build artifacts
clean:
	@echo "Cleaning build artifacts..."
	rm -rf bin/
	rm -rf coverage.out coverage.html
	rm -rf docs/docs.go docs/swagger.json docs/swagger.yaml

# Install development tools
install-tools:
	@echo "Installing development tools..."
	go install github.com/swaggo/swag/cmd/swag@latest
	go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
	go install github.com/securecodewarrior/gosec/v2/cmd/gosec@latest

# Help
help:
	@echo "Available targets:"
	@echo "  setup           - Set up project structure and dependencies"
	@echo "  build           - Build the application"
	@echo "  build-all       - Build for multiple platforms"
	@echo "  run             - Run the application"
	@echo "  dev             - Run in development mode"
	@echo "  test            - Run tests"
	@echo "  test-coverage   - Run tests with coverage report"
	@echo "  test-integration- Run integration tests"
	@echo "  test-load       - Run load tests"
	@echo "  generate-docs   - Generate API documentation"
	@echo "  fmt             - Format code"
	@echo "  lint            - Lint code"
	@echo "  security        - Run security scan"
	@echo "  docker-build    - Build Docker image"
	@echo "  docker-up       - Start with Docker Compose"
	@echo "  docker-down     - Stop Docker Compose"
	@echo "  clean           - Clean build artifacts"
	@echo "  install-tools   - Install development tools"
	@echo "  help            - Show this help"
