@echo off
echo Initializing Go dependencies for AI Studio Proxy...

echo Running go get commands as suggested by Go toolchain...

go get gopkg.in/yaml.v3
go get github.com/playwright-community/playwright-go
go get github.com/sirupsen/logrus
go get github.com/google/uuid
go get github.com/gin-gonic/gin
go get golang.org/x/time/rate
go get github.com/swaggo/files
go get github.com/swaggo/gin-swagger
go get github.com/swaggo/swag
go get github.com/prometheus/client_golang
go get github.com/stretchr/testify

echo Running go mod tidy...
go mod tidy

echo Attempting build...
go build -o bin\aistudio-proxy.exe cmd\server\main.go

if exist bin\aistudio-proxy.exe (
    echo SUCCESS: Binary created at bin\aistudio-proxy.exe
    dir bin\aistudio-proxy.exe
) else (
    echo FAILED: Binary not created
)
