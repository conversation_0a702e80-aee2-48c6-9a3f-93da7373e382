#!/usr/bin/env powershell

Write-Host "Building AI Studio Proxy..." -ForegroundColor Green

# Create bin directory if it doesn't exist
if (!(Test-Path "bin")) {
    New-Item -ItemType Directory -Path "bin" -Force
}

Write-Host "Step 1: Downloading individual dependencies..." -ForegroundColor Yellow

# Download dependencies individually as suggested by Go
$dependencies = @(
    "gopkg.in/yaml.v3",
    "github.com/playwright-community/playwright-go",
    "github.com/sirupsen/logrus",
    "github.com/google/uuid",
    "github.com/gin-gonic/gin",
    "golang.org/x/time",
    "github.com/swaggo/files",
    "github.com/swaggo/gin-swagger",
    "github.com/swaggo/swag",
    "github.com/prometheus/client_golang",
    "github.com/stretchr/testify"
)

foreach ($dep in $dependencies) {
    Write-Host "Downloading $dep..." -ForegroundColor Cyan
    go mod download $dep
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Failed to download $dep" -ForegroundColor Red
    }
}

Write-Host "Step 2: Running go mod tidy..." -ForegroundColor Yellow
go mod tidy

Write-Host "Step 3: Attempting to build..." -ForegroundColor Yellow
go build -o bin/aistudio-proxy.exe cmd/server/main.go

if ($LASTEXITCODE -eq 0) {
    Write-Host "Build successful!" -ForegroundColor Green
    Write-Host "Binary created: bin/aistudio-proxy.exe" -ForegroundColor Green
    
    # Show file info
    if (Test-Path "bin/aistudio-proxy.exe") {
        $fileInfo = Get-Item "bin/aistudio-proxy.exe"
        Write-Host "File size: $($fileInfo.Length) bytes" -ForegroundColor Green
        Write-Host "Created: $($fileInfo.CreationTime)" -ForegroundColor Green
    }
} else {
    Write-Host "Build failed with exit code: $LASTEXITCODE" -ForegroundColor Red
    exit 1
}

Write-Host "Step 4: Running basic validation tests..." -ForegroundColor Yellow

# Test configuration loading
Write-Host "Testing configuration..." -ForegroundColor Cyan
go run -c "
package main
import (
    `"fmt`"
    `"aistudio-proxy/internal/config`"
)
func main() {
    cfg := config.Config{}
    fmt.Println(`"Configuration structure accessible`")
}
"

Write-Host "Build and test process completed!" -ForegroundColor Green
