package aistudio

import (
	"testing"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"

	"aistudio-proxy/internal/config"
)

// MockBrowserManager for testing
type MockBrowserManager struct{}

func (m *MockBrowserManager) GetContext(sessionID string) (*MockBrowserContext, error) {
	return &MockBrowserContext{SessionID: sessionID}, nil
}

type MockBrowserContext struct {
	SessionID string
}

// TestClientCreation tests that the AI Studio client can be created with correct configuration
func TestClientCreation(t *testing.T) {
	cfg := &config.Config{
		AIStudio: config.AIStudioConfig{
			BaseURL:        "https://aistudio.google.com",
			Email:          "<EMAIL>",
			Password:       "test-password",
			DefaultModel:   "gemini-pro",
			RequestTimeout: 30 * time.Second,
			UploadTimeout:  60 * time.Second,
		},
	}

	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel) // Reduce noise in tests

	mockBrowserManager := &MockBrowserManager{}

	client := NewClient(cfg, logger, mockBrowserManager)

	assert.NotNil(t, client)
	assert.Equal(t, cfg, client.config)
	assert.Equal(t, logger, client.logger)
}

// TestChatRequestValidation tests chat request structure
func TestChatRequestValidation(t *testing.T) {
	tests := []struct {
		name    string
		request ChatRequest
		valid   bool
	}{
		{
			name: "Valid basic request",
			request: ChatRequest{
				Message: "Hello, world!",
				Model:   "gemini-pro",
			},
			valid: true,
		},
		{
			name: "Valid request with session ID",
			request: ChatRequest{
				Message:   "Hello, world!",
				Model:     "gemini-pro",
				SessionID: "test-session-123",
			},
			valid: true,
		},
		{
			name: "Valid request with file",
			request: ChatRequest{
				Message: "Analyze this file",
				Model:   "gemini-pro",
				File: &File{
					Name:     "test.txt",
					Content:  "dGVzdCBjb250ZW50", // base64 for "test content"
					MimeType: "text/plain",
					Size:     12,
				},
			},
			valid: true,
		},
		{
			name: "Empty message",
			request: ChatRequest{
				Message: "",
				Model:   "gemini-pro",
			},
			valid: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Basic validation - in real implementation this would be more comprehensive
			isValid := tt.request.Message != ""
			assert.Equal(t, tt.valid, isValid)
		})
	}
}

// TestChatResponseStructure tests chat response structure
func TestChatResponseStructure(t *testing.T) {
	response := &ChatResponse{
		Response:  "Hello! How can I help you today?",
		SessionID: "test-session-123",
		ModelUsed: "gemini-pro",
		Timestamp: time.Now(),
		Status:    "success",
	}

	assert.Equal(t, "Hello! How can I help you today?", response.Response)
	assert.Equal(t, "test-session-123", response.SessionID)
	assert.Equal(t, "gemini-pro", response.ModelUsed)
	assert.Equal(t, "success", response.Status)
	assert.False(t, response.Timestamp.IsZero())
}

// TestModelStructure tests model information structure
func TestModelStructure(t *testing.T) {
	model := Model{
		ID:           "gemini-pro",
		Name:         "Gemini Pro",
		Description:  "Most capable model for complex reasoning tasks",
		Capabilities: []string{"text", "images", "documents"},
	}

	assert.Equal(t, "gemini-pro", model.ID)
	assert.Equal(t, "Gemini Pro", model.Name)
	assert.Contains(t, model.Capabilities, "text")
	assert.Contains(t, model.Capabilities, "images")
	assert.Contains(t, model.Capabilities, "documents")
}

// TestFileStructure tests file upload structure
func TestFileStructure(t *testing.T) {
	file := &File{
		Name:     "test-document.pdf",
		Content:  "base64-encoded-content-here",
		MimeType: "application/pdf",
		Size:     1024,
	}

	assert.Equal(t, "test-document.pdf", file.Name)
	assert.Equal(t, "application/pdf", file.MimeType)
	assert.Equal(t, int64(1024), file.Size)
	assert.NotEmpty(t, file.Content)
}

// TestAPIErrorStructure tests API error structure
func TestAPIErrorStructure(t *testing.T) {
	apiError := &APIError{
		Code:    "RATE_LIMIT_EXCEEDED",
		Message: "Rate limit exceeded. Please try again later.",
		Details: map[string]interface{}{
			"retry_after": 60,
			"limit":       10,
		},
	}

	assert.Equal(t, "RATE_LIMIT_EXCEEDED", apiError.Code)
	assert.Contains(t, apiError.Message, "Rate limit exceeded")
	assert.NotNil(t, apiError.Details)
}
