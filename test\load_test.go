//go:build load
// +build load

package test

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"sync"
	"sync/atomic"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"aistudio-proxy/internal/session"
)

const (
	loadTestBaseURL = "http://localhost:8080"
	loadTestAPIKey  = "test-api-key"
)

// LoadTestConfig holds load test configuration
type LoadTestConfig struct {
	Duration       time.Duration
	Concurrency    int
	RequestsPerSec int
	Endpoint       string
}

// LoadTestResult holds load test results
type LoadTestResult struct {
	TotalRequests    int64
	SuccessRequests  int64
	FailedRequests   int64
	AverageLatency   time.Duration
	MinLatency       time.Duration
	MaxLatency       time.Duration
	RequestsPerSec   float64
	ErrorRate        float64
}

// TestLoadHealthEndpoint performs load testing on the health endpoint
func TestLoadHealthEndpoint(t *testing.T) {
	config := LoadTestConfig{
		Duration:       30 * time.Second,
		Concurrency:    50,
		RequestsPerSec: 100,
		Endpoint:       "/health",
	}

	result := runLoadTest(t, config, func(client *http.Client) error {
		resp, err := client.Get(loadTestBaseURL + config.Endpoint)
		if err != nil {
			return err
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			return fmt.Errorf("unexpected status code: %d", resp.StatusCode)
		}

		return nil
	})

	// Assert performance requirements
	assert.Less(t, result.ErrorRate, 0.01, "Error rate should be less than 1%")
	assert.Greater(t, result.RequestsPerSec, 50.0, "Should handle at least 50 requests per second")
	assert.Less(t, result.AverageLatency, 100*time.Millisecond, "Average latency should be less than 100ms")

	logLoadTestResult(t, "Health Endpoint", result)
}

// TestLoadSessionCreation performs load testing on session creation
func TestLoadSessionCreation(t *testing.T) {
	config := LoadTestConfig{
		Duration:       60 * time.Second,
		Concurrency:    20,
		RequestsPerSec: 10,
		Endpoint:       "/api/v1/session",
	}

	sessionReq := session.CreateSessionRequest{
		Model:      "gemini-pro",
		TTLMinutes: 30,
	}
	reqBody, _ := json.Marshal(sessionReq)

	result := runLoadTest(t, config, func(client *http.Client) error {
		req, _ := http.NewRequest("POST", loadTestBaseURL+config.Endpoint, bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer "+loadTestAPIKey)

		resp, err := client.Do(req)
		if err != nil {
			return err
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			return fmt.Errorf("unexpected status code: %d", resp.StatusCode)
		}

		return nil
	})

	// Assert performance requirements for session creation
	assert.Less(t, result.ErrorRate, 0.05, "Error rate should be less than 5%")
	assert.Greater(t, result.RequestsPerSec, 5.0, "Should handle at least 5 session creations per second")
	assert.Less(t, result.AverageLatency, 500*time.Millisecond, "Average latency should be less than 500ms")

	logLoadTestResult(t, "Session Creation", result)
}

// TestLoadConcurrentSessions tests handling of concurrent sessions
func TestLoadConcurrentSessions(t *testing.T) {
	numSessions := 100
	client := &http.Client{Timeout: 30 * time.Second}

	// Create sessions concurrently
	var wg sync.WaitGroup
	var successCount int64
	var errorCount int64

	sessionReq := session.CreateSessionRequest{
		Model:      "gemini-pro",
		TTLMinutes: 10,
	}
	reqBody, _ := json.Marshal(sessionReq)

	start := time.Now()

	for i := 0; i < numSessions; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()

			req, _ := http.NewRequest("POST", loadTestBaseURL+"/api/v1/session", bytes.NewBuffer(reqBody))
			req.Header.Set("Content-Type", "application/json")
			req.Header.Set("Authorization", "Bearer "+loadTestAPIKey)

			resp, err := client.Do(req)
			if err != nil {
				atomic.AddInt64(&errorCount, 1)
				return
			}
			defer resp.Body.Close()

			if resp.StatusCode == http.StatusOK {
				atomic.AddInt64(&successCount, 1)
			} else {
				atomic.AddInt64(&errorCount, 1)
			}
		}(i)
	}

	wg.Wait()
	duration := time.Since(start)

	successRate := float64(successCount) / float64(numSessions)
	sessionsPerSec := float64(numSessions) / duration.Seconds()

	t.Logf("Concurrent Sessions Test Results:")
	t.Logf("  Total Sessions: %d", numSessions)
	t.Logf("  Successful: %d", successCount)
	t.Logf("  Failed: %d", errorCount)
	t.Logf("  Success Rate: %.2f%%", successRate*100)
	t.Logf("  Sessions/sec: %.2f", sessionsPerSec)
	t.Logf("  Duration: %v", duration)

	// Assert requirements
	assert.Greater(t, successRate, 0.9, "Success rate should be greater than 90%")
	assert.Greater(t, sessionsPerSec, 10.0, "Should handle at least 10 sessions per second")
}

// TestLoadMemoryUsage tests memory usage under load
func TestLoadMemoryUsage(t *testing.T) {
	// This would require integration with runtime metrics
	// For now, we'll simulate a sustained load test

	config := LoadTestConfig{
		Duration:       5 * time.Minute,
		Concurrency:    10,
		RequestsPerSec: 20,
		Endpoint:       "/health",
	}

	result := runLoadTest(t, config, func(client *http.Client) error {
		resp, err := client.Get(loadTestBaseURL + config.Endpoint)
		if err != nil {
			return err
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			return fmt.Errorf("unexpected status code: %d", resp.StatusCode)
		}

		return nil
	})

	// Assert sustained performance
	assert.Less(t, result.ErrorRate, 0.01, "Error rate should remain low during sustained load")
	assert.Greater(t, result.RequestsPerSec, 15.0, "Should maintain throughput during sustained load")

	logLoadTestResult(t, "Memory Usage (Sustained Load)", result)
}

// runLoadTest executes a load test with the given configuration
func runLoadTest(t *testing.T, config LoadTestConfig, requestFunc func(*http.Client) error) LoadTestResult {
	var totalRequests int64
	var successRequests int64
	var failedRequests int64
	var totalLatency int64
	var minLatency int64 = int64(time.Hour) // Initialize to a large value
	var maxLatency int64

	client := &http.Client{Timeout: 30 * time.Second}
	
	// Rate limiter
	ticker := time.NewTicker(time.Second / time.Duration(config.RequestsPerSec))
	defer ticker.Stop()

	// Worker pool
	requestChan := make(chan struct{}, config.Concurrency)
	var wg sync.WaitGroup

	// Start time
	start := time.Now()
	endTime := start.Add(config.Duration)

	// Launch workers
	for i := 0; i < config.Concurrency; i++ {
		go func() {
			for range requestChan {
				wg.Add(1)
				go func() {
					defer wg.Done()

					requestStart := time.Now()
					err := requestFunc(client)
					latency := time.Since(requestStart)

					atomic.AddInt64(&totalRequests, 1)
					atomic.AddInt64(&totalLatency, int64(latency))

					// Update min/max latency
					latencyNs := int64(latency)
					for {
						currentMin := atomic.LoadInt64(&minLatency)
						if latencyNs >= currentMin || atomic.CompareAndSwapInt64(&minLatency, currentMin, latencyNs) {
							break
						}
					}
					for {
						currentMax := atomic.LoadInt64(&maxLatency)
						if latencyNs <= currentMax || atomic.CompareAndSwapInt64(&maxLatency, currentMax, latencyNs) {
							break
						}
					}

					if err != nil {
						atomic.AddInt64(&failedRequests, 1)
					} else {
						atomic.AddInt64(&successRequests, 1)
					}
				}()
			}
		}()
	}

	// Send requests at the specified rate
	for time.Now().Before(endTime) {
		select {
		case requestChan <- struct{}{}:
		case <-ticker.C:
		}
	}

	close(requestChan)
	wg.Wait()

	duration := time.Since(start)

	// Calculate results
	total := atomic.LoadInt64(&totalRequests)
	success := atomic.LoadInt64(&successRequests)
	failed := atomic.LoadInt64(&failedRequests)
	avgLatency := time.Duration(atomic.LoadInt64(&totalLatency) / total)
	minLat := time.Duration(atomic.LoadInt64(&minLatency))
	maxLat := time.Duration(atomic.LoadInt64(&maxLatency))
	requestsPerSec := float64(total) / duration.Seconds()
	errorRate := float64(failed) / float64(total)

	return LoadTestResult{
		TotalRequests:   total,
		SuccessRequests: success,
		FailedRequests:  failed,
		AverageLatency:  avgLatency,
		MinLatency:      minLat,
		MaxLatency:      maxLat,
		RequestsPerSec:  requestsPerSec,
		ErrorRate:       errorRate,
	}
}

// logLoadTestResult logs the load test results
func logLoadTestResult(t *testing.T, testName string, result LoadTestResult) {
	t.Logf("%s Load Test Results:", testName)
	t.Logf("  Total Requests: %d", result.TotalRequests)
	t.Logf("  Successful: %d", result.SuccessRequests)
	t.Logf("  Failed: %d", result.FailedRequests)
	t.Logf("  Requests/sec: %.2f", result.RequestsPerSec)
	t.Logf("  Error Rate: %.2f%%", result.ErrorRate*100)
	t.Logf("  Average Latency: %v", result.AverageLatency)
	t.Logf("  Min Latency: %v", result.MinLatency)
	t.Logf("  Max Latency: %v", result.MaxLatency)
}
